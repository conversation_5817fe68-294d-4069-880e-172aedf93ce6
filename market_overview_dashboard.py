"""
Market Overview Dashboard for Erica's Trading System

Provides comprehensive market overview with daily best strategies,
market environment summary, and actionable trading plans.
"""

import tkinter as tk
from tkinter import ttk, scrolledtext
from typing import Dict, List, Optional, Any
from datetime import datetime
import threading

from market_intelligence import MarketIntelligenceEngine, MarketIntelligence
from strategy_knowledge_base import StrategyKnowledgeBase
from intelligent_strategy_engine import IntelligentStrategyEngine

class MarketOverviewWidget(ttk.Frame):
    """Market Overview Dashboard Widget"""
    
    def __init__(self, parent, api_key: str, **kwargs):
        super().__init__(parent, **kwargs)
        
        self.api_key = api_key
        self.intelligence_engine = MarketIntelligenceEngine(api_key)
        self.strategy_engine = IntelligentStrategyEngine(api_key)
        self.strategy_kb = StrategyKnowledgeBase()
        
        self.current_intelligence: Optional[MarketIntelligence] = None
        
        self.setup_ui()
        self.refresh_data()
    
    def setup_ui(self):
        """Setup the market overview user interface"""
        
        # Main container with scrollable content
        main_frame = ttk.Frame(self)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Header
        header_frame = ttk.Frame(main_frame)
        header_frame.pack(fill=tk.X, pady=(0, 10))
        
        title_label = ttk.Label(header_frame, text="📊 Market Overview & Daily Strategy Plan", 
                               font=('Arial', 16, 'bold'))
        title_label.pack(side=tk.LEFT)
        
        self.last_update_label = ttk.Label(header_frame, text="Last Updated: --", 
                                          font=('Arial', 10))
        self.last_update_label.pack(side=tk.RIGHT)
        
        refresh_btn = ttk.Button(header_frame, text="🔄 Refresh", command=self.refresh_data)
        refresh_btn.pack(side=tk.RIGHT, padx=(0, 10))
        
        # Create notebook for different sections
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # Market Environment Tab
        self.create_market_environment_tab()
        
        # Daily Strategy Plan Tab
        self.create_strategy_plan_tab()
        
        # Sector Analysis Tab
        self.create_sector_analysis_tab()
        
        # Risk Assessment Tab
        self.create_risk_assessment_tab()
    
    def create_market_environment_tab(self):
        """Create market environment analysis tab"""
        
        env_frame = ttk.Frame(self.notebook)
        self.notebook.add(env_frame, text="Market Environment")
        
        # Market Regime Section
        regime_frame = ttk.LabelFrame(env_frame, text="Current Market Regime")
        regime_frame.pack(fill=tk.X, padx=5, pady=5)
        
        regime_grid = ttk.Frame(regime_frame)
        regime_grid.pack(fill=tk.X, padx=10, pady=10)
        
        # Regime indicators
        self.regime_labels = {}
        regime_metrics = [
            ("Regime", "regime"),
            ("Trend", "trend"), 
            ("Volatility", "volatility"),
            ("Confidence", "confidence")
        ]
        
        for i, (label, key) in enumerate(regime_metrics):
            row = i // 2
            col = (i % 2) * 2
            
            ttk.Label(regime_grid, text=f"{label}:", font=('Arial', 10, 'bold')).grid(
                row=row, column=col, sticky='w', padx=5, pady=2)
            self.regime_labels[key] = ttk.Label(regime_grid, text="--", font=('Arial', 10))
            self.regime_labels[key].grid(row=row, column=col+1, sticky='w', padx=5, pady=2)
        
        # Market Metrics Section
        metrics_frame = ttk.LabelFrame(env_frame, text="Key Market Metrics")
        metrics_frame.pack(fill=tk.X, padx=5, pady=5)
        
        metrics_grid = ttk.Frame(metrics_frame)
        metrics_grid.pack(fill=tk.X, padx=10, pady=10)
        
        self.metric_labels = {}
        market_metrics = [
            ("VIX Level", "vix"),
            ("VIX Percentile", "vix_pct"),
            ("SPY Strength", "spy_strength"),
            ("Market Breadth", "breadth")
        ]
        
        for i, (label, key) in enumerate(market_metrics):
            row = i // 2
            col = (i % 2) * 2
            
            ttk.Label(metrics_grid, text=f"{label}:", font=('Arial', 10, 'bold')).grid(
                row=row, column=col, sticky='w', padx=5, pady=2)
            self.metric_labels[key] = ttk.Label(metrics_grid, text="--", font=('Arial', 10))
            self.metric_labels[key].grid(row=row, column=col+1, sticky='w', padx=5, pady=2)
        
        # Market Commentary
        commentary_frame = ttk.LabelFrame(env_frame, text="Market Commentary")
        commentary_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.commentary_text = scrolledtext.ScrolledText(commentary_frame, height=8, 
                                                        font=('Arial', 10), wrap=tk.WORD)
        self.commentary_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
    
    def create_strategy_plan_tab(self):
        """Create daily strategy plan tab"""
        
        strategy_frame = ttk.Frame(self.notebook)
        self.notebook.add(strategy_frame, text="Daily Strategy Plan")
        
        # Best Strategies Section
        best_frame = ttk.LabelFrame(strategy_frame, text="🎯 Today's Best Strategies")
        best_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.strategy_tree = ttk.Treeview(best_frame, columns=('Strategy', 'Rationale', 'Risk'), 
                                         show='headings', height=6)
        
        self.strategy_tree.heading('Strategy', text='Strategy')
        self.strategy_tree.heading('Rationale', text='Rationale')
        self.strategy_tree.heading('Risk', text='Risk Level')
        
        self.strategy_tree.column('Strategy', width=150)
        self.strategy_tree.column('Rationale', width=300)
        self.strategy_tree.column('Risk', width=100)
        
        self.strategy_tree.pack(fill=tk.X, padx=5, pady=5)
        
        # Symbol-Specific Recommendations
        symbols_frame = ttk.LabelFrame(strategy_frame, text="📈 Symbol-Specific Recommendations")
        symbols_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.symbols_tree = ttk.Treeview(symbols_frame, columns=('Symbol', 'Strategy', 'Confidence', 'IV Rank'), 
                                        show='headings', height=8)
        
        self.symbols_tree.heading('Symbol', text='Symbol')
        self.symbols_tree.heading('Strategy', text='Strategy')
        self.symbols_tree.heading('Confidence', text='Confidence')
        self.symbols_tree.heading('IV Rank', text='IV Rank')
        
        for col in ('Symbol', 'Strategy', 'Confidence', 'IV Rank'):
            self.symbols_tree.column(col, width=120)
        
        self.symbols_tree.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Action Plan
        action_frame = ttk.LabelFrame(strategy_frame, text="📋 Today's Action Plan")
        action_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.action_text = scrolledtext.ScrolledText(action_frame, height=6, 
                                                    font=('Arial', 10), wrap=tk.WORD)
        self.action_text.pack(fill=tk.X, padx=5, pady=5)
    
    def create_sector_analysis_tab(self):
        """Create sector analysis tab"""
        
        sector_frame = ttk.Frame(self.notebook)
        self.notebook.add(sector_frame, text="Sector Analysis")
        
        # Leading/Lagging Sectors
        leaders_frame = ttk.LabelFrame(sector_frame, text="🚀 Leading Sectors")
        leaders_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.leaders_text = tk.Text(leaders_frame, height=4, font=('Arial', 10), wrap=tk.WORD)
        self.leaders_text.pack(fill=tk.X, padx=5, pady=5)
        
        laggards_frame = ttk.LabelFrame(sector_frame, text="📉 Lagging Sectors")
        laggards_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.laggards_text = tk.Text(laggards_frame, height=4, font=('Arial', 10), wrap=tk.WORD)
        self.laggards_text.pack(fill=tk.X, padx=5, pady=5)
        
        # Rotation Analysis
        rotation_frame = ttk.LabelFrame(sector_frame, text="🔄 Rotation Analysis")
        rotation_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.rotation_text = scrolledtext.ScrolledText(rotation_frame, height=10, 
                                                      font=('Arial', 10), wrap=tk.WORD)
        self.rotation_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
    
    def create_risk_assessment_tab(self):
        """Create risk assessment tab"""
        
        risk_frame = ttk.Frame(self.notebook)
        self.notebook.add(risk_frame, text="Risk Assessment")
        
        # Risk Level Indicator
        level_frame = ttk.LabelFrame(risk_frame, text="⚠️ Current Risk Level")
        level_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.risk_level_label = ttk.Label(level_frame, text="--", 
                                         font=('Arial', 14, 'bold'))
        self.risk_level_label.pack(pady=10)
        
        # Risk Factors
        factors_frame = ttk.LabelFrame(risk_frame, text="Risk Factors")
        factors_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.risk_factors_text = tk.Text(factors_frame, height=6, font=('Arial', 10), wrap=tk.WORD)
        self.risk_factors_text.pack(fill=tk.X, padx=5, pady=5)
        
        # Position Sizing Guidance
        sizing_frame = ttk.LabelFrame(risk_frame, text="📏 Position Sizing Guidance")
        sizing_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.sizing_text = scrolledtext.ScrolledText(sizing_frame, height=8, 
                                                    font=('Arial', 10), wrap=tk.WORD)
        self.sizing_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
    
    def refresh_data(self):
        """Refresh all market data and analysis"""
        
        def refresh_worker():
            try:
                # Update status
                self.after(0, lambda: self.last_update_label.config(text="Updating..."))
                
                # Get market intelligence
                intelligence = self.intelligence_engine.get_market_intelligence(force_refresh=True)
                self.current_intelligence = intelligence
                
                # Get symbol recommendations
                symbols = ["AMD", "NVDA", "GOOGL", "AAPL", "AMZN"]
                market_report, strategies = self.strategy_engine.generate_daily_recommendations(symbols)
                
                # Update UI on main thread
                self.after(0, lambda: self._update_all_displays(intelligence, strategies))
                
                # Update timestamp
                update_time = datetime.now().strftime('%H:%M:%S')
                self.after(0, lambda: self.last_update_label.config(text=f"Last Updated: {update_time}"))
                
            except Exception as e:
                error_msg = f"Error: {str(e)[:50]}..."
                self.after(0, lambda: self.last_update_label.config(text=error_msg))
        
        # Run in background thread
        threading.Thread(target=refresh_worker, daemon=True).start()
    
    def _update_all_displays(self, intelligence: MarketIntelligence, strategies: List):
        """Update all display components with new data"""
        
        self._update_market_environment(intelligence)
        self._update_strategy_plan(intelligence, strategies)
        self._update_sector_analysis(intelligence)
        self._update_risk_assessment(intelligence)
    
    def _update_market_environment(self, intelligence: MarketIntelligence):
        """Update market environment tab"""
        
        # Update regime labels
        self.regime_labels["regime"].config(text=intelligence.current_regime.value.replace('_', ' ').title())
        self.regime_labels["trend"].config(text=intelligence.trend_direction.value.replace('_', ' ').title())
        self.regime_labels["volatility"].config(text=intelligence.volatility_regime.value.replace('_', ' ').title())
        self.regime_labels["confidence"].config(text=f"{intelligence.regime_confidence:.0%}")
        
        # Update metric labels
        self.metric_labels["vix"].config(text=f"{intelligence.vix_level:.1f}")
        self.metric_labels["vix_pct"].config(text=f"{intelligence.vix_percentile:.0f}%")
        self.metric_labels["spy_strength"].config(text=f"{intelligence.spy_trend_strength:.0%}")
        self.metric_labels["breadth"].config(text=f"{intelligence.market_breadth:.0%}")
        
        # Update commentary
        self.commentary_text.delete(1.0, tk.END)
        commentary = self._generate_market_commentary(intelligence)
        self.commentary_text.insert(tk.END, commentary)
    
    def _update_strategy_plan(self, intelligence: MarketIntelligence, strategies: List):
        """Update strategy plan tab"""
        
        # Clear existing items
        for item in self.strategy_tree.get_children():
            self.strategy_tree.delete(item)
        
        for item in self.symbols_tree.get_children():
            self.symbols_tree.delete(item)
        
        # Add best strategies
        for strategy_type in intelligence.best_strategy_types:
            strategy_info = self.strategy_kb.strategies.get(strategy_type)
            if strategy_info:
                self.strategy_tree.insert('', 'end', values=(
                    strategy_info.name,
                    strategy_info.description[:50] + "...",
                    intelligence.risk_level
                ))
        
        # Add symbol-specific recommendations
        for strategy in strategies:
            # Get stock factors for IV rank
            try:
                stock_factors = self.strategy_engine.market_analyzer.analyze_stock_factors(strategy.symbol)
                iv_rank = f"{stock_factors.iv_rank:.0%}"
            except:
                iv_rank = "--"
            
            self.symbols_tree.insert('', 'end', values=(
                strategy.symbol,
                strategy.recommended_strategy.value.replace('_', ' ').title(),
                f"{strategy.confidence:.0%}",
                iv_rank
            ))
        
        # Update action plan
        self.action_text.delete(1.0, tk.END)
        action_plan = self._generate_action_plan(intelligence, strategies)
        self.action_text.insert(tk.END, action_plan)
    
    def _update_sector_analysis(self, intelligence: MarketIntelligence):
        """Update sector analysis tab"""
        
        # Update leaders
        self.leaders_text.delete(1.0, tk.END)
        leaders_text = "Leading Sectors:\n" + "\n".join([f"• {sector}" for sector in intelligence.leading_sectors])
        self.leaders_text.insert(tk.END, leaders_text)
        
        # Update laggards
        self.laggards_text.delete(1.0, tk.END)
        laggards_text = "Lagging Sectors:\n" + "\n".join([f"• {sector}" for sector in intelligence.lagging_sectors])
        self.laggards_text.insert(tk.END, laggards_text)
        
        # Update rotation analysis
        self.rotation_text.delete(1.0, tk.END)
        rotation_analysis = self._generate_rotation_analysis(intelligence)
        self.rotation_text.insert(tk.END, rotation_analysis)
    
    def _update_risk_assessment(self, intelligence: MarketIntelligence):
        """Update risk assessment tab"""
        
        # Update risk level with color coding
        self.risk_level_label.config(text=intelligence.risk_level)
        
        if intelligence.risk_level == "High":
            self.risk_level_label.config(foreground="red")
        elif intelligence.risk_level == "Moderate":
            self.risk_level_label.config(foreground="orange")
        else:
            self.risk_level_label.config(foreground="green")
        
        # Update risk factors
        self.risk_factors_text.delete(1.0, tk.END)
        risk_factors = self._generate_risk_factors(intelligence)
        self.risk_factors_text.insert(tk.END, risk_factors)
        
        # Update position sizing guidance
        self.sizing_text.delete(1.0, tk.END)
        sizing_guidance = self._generate_sizing_guidance(intelligence)
        self.sizing_text.insert(tk.END, sizing_guidance)
    
    def _generate_market_commentary(self, intelligence: MarketIntelligence) -> str:
        """Generate market commentary text"""
        
        commentary = f"Market Analysis - {intelligence.timestamp.strftime('%Y-%m-%d %H:%M')}\n\n"
        
        commentary += f"Current Environment:\n"
        commentary += f"• Market Regime: {intelligence.current_regime.value.replace('_', ' ').title()}\n"
        commentary += f"• Trend Direction: {intelligence.trend_direction.value.replace('_', ' ').title()}\n"
        commentary += f"• Volatility Regime: {intelligence.volatility_regime.value.replace('_', ' ').title()}\n\n"
        
        commentary += f"Strategy Implications:\n"
        commentary += f"• {intelligence.strategy_rationale}\n"
        commentary += f"• Risk Level: {intelligence.risk_level}\n"
        commentary += f"• Best Strategies: {', '.join(intelligence.best_strategy_types)}\n\n"
        
        if intelligence.major_events:
            commentary += f"Upcoming Events:\n"
            for event in intelligence.major_events:
                commentary += f"• {event}\n"
        
        return commentary
    
    def _generate_action_plan(self, intelligence: MarketIntelligence, strategies: List) -> str:
        """Generate actionable trading plan"""
        
        plan = "Today's Trading Action Plan:\n\n"
        
        plan += "1. PRIORITY TRADES:\n"
        high_conf_strategies = [s for s in strategies if s.confidence >= 0.7]
        for strategy in high_conf_strategies[:3]:
            plan += f"   • {strategy.symbol}: {strategy.recommended_strategy.value.replace('_', ' ').title()} "
            plan += f"(Confidence: {strategy.confidence:.0%})\n"
        
        plan += "\n2. MARKET CONDITIONS:\n"
        plan += f"   • {intelligence.strategy_rationale}\n"
        plan += f"   • Risk Level: {intelligence.risk_level}\n"
        
        plan += "\n3. POSITION SIZING:\n"
        if intelligence.risk_level == "High":
            plan += "   • Reduce position sizes by 50%\n"
            plan += "   • Focus on high-probability setups only\n"
        elif intelligence.risk_level == "Moderate":
            plan += "   • Use standard position sizing\n"
            plan += "   • Monitor closely for changes\n"
        else:
            plan += "   • Normal position sizing acceptable\n"
            plan += "   • Consider scaling into positions\n"
        
        plan += "\n4. KEY LEVELS TO WATCH:\n"
        plan += f"   • VIX: {intelligence.vix_level:.1f} (watch for moves above/below)\n"
        plan += f"   • SPY technical strength: {intelligence.spy_trend_strength:.0%}\n"
        
        return plan
    
    def _generate_rotation_analysis(self, intelligence: MarketIntelligence) -> str:
        """Generate sector rotation analysis"""
        
        analysis = "Sector Rotation Analysis:\n\n"
        
        analysis += f"Current Signal: {intelligence.sector_rotation_signal.title()}\n\n"
        
        analysis += "Leadership Analysis:\n"
        analysis += f"Leading Sectors: {', '.join(intelligence.leading_sectors)}\n"
        analysis += f"Lagging Sectors: {', '.join(intelligence.lagging_sectors)}\n\n"
        
        analysis += "Implications:\n"
        if "Technology" in intelligence.leading_sectors:
            analysis += "• Technology leadership suggests growth/risk-on environment\n"
        if "Utilities" in intelligence.leading_sectors:
            analysis += "• Utilities leadership suggests defensive positioning\n"
        if "Financials" in intelligence.leading_sectors:
            analysis += "• Financial strength suggests rising rate expectations\n"
        
        analysis += f"\nStrategy Impact:\n"
        analysis += f"• Current trend supports {intelligence.trend_direction.value.replace('_', ' ')} strategies\n"
        analysis += f"• Sector rotation {intelligence.sector_rotation_signal} supports current positioning\n"
        
        return analysis
    
    def _generate_risk_factors(self, intelligence: MarketIntelligence) -> str:
        """Generate risk factors analysis"""
        
        factors = "Current Risk Factors:\n\n"
        
        if intelligence.vix_level > 25:
            factors += "• Elevated volatility (VIX > 25) increases position risk\n"
        
        if intelligence.fed_meeting_proximity <= 7:
            factors += "• Fed meeting within 7 days - expect increased volatility\n"
        
        if intelligence.earnings_season_intensity > 0.6:
            factors += "• Heavy earnings season - individual stock risk elevated\n"
        
        if intelligence.trend_direction in ["bearish", "strong_bearish"]:
            factors += "• Bearish market trend increases downside risk\n"
        
        if not any([intelligence.vix_level > 25, intelligence.fed_meeting_proximity <= 7, 
                   intelligence.earnings_season_intensity > 0.6]):
            factors += "• No major risk factors identified\n"
            factors += "• Market conditions relatively stable\n"
        
        return factors
    
    def _generate_sizing_guidance(self, intelligence: MarketIntelligence) -> str:
        """Generate position sizing guidance"""
        
        guidance = "Position Sizing Recommendations:\n\n"
        
        if intelligence.risk_level == "High":
            guidance += "HIGH RISK ENVIRONMENT:\n"
            guidance += "• Reduce position sizes to 50% of normal\n"
            guidance += "• Maximum 1% risk per trade\n"
            guidance += "• Focus on highest probability setups only\n"
            guidance += "• Consider cash positions\n"
        
        elif intelligence.risk_level == "Moderate":
            guidance += "MODERATE RISK ENVIRONMENT:\n"
            guidance += "• Use standard position sizing (2% risk per trade)\n"
            guidance += "• Monitor positions closely\n"
            guidance += "• Be ready to reduce size if conditions deteriorate\n"
            guidance += "• Diversify across strategies\n"
        
        else:
            guidance += "LOW RISK ENVIRONMENT:\n"
            guidance += "• Normal position sizing acceptable\n"
            guidance += "• Consider scaling into larger positions\n"
            guidance += "• Up to 3% risk per trade on best setups\n"
            guidance += "• Opportunity to be more aggressive\n"
        
        guidance += f"\nCurrent Market Conditions:\n"
        guidance += f"• Volatility: {intelligence.volatility_regime.value.replace('_', ' ').title()}\n"
        guidance += f"• Trend: {intelligence.trend_direction.value.replace('_', ' ').title()}\n"
        guidance += f"• Best Strategies: {', '.join(intelligence.best_strategy_types)}\n"
        
        return guidance
