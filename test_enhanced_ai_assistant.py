"""
Test Suite for Enhanced Options AI Assistant
Comprehensive testing of the enhanced AI assistant functionality

This test suite validates:
1. Market data integration and real-time updates
2. News catalyst analysis and classification
3. Options position tracking and analysis
4. Strategic recommendations generation
5. Portfolio risk assessment
6. GUI integration functionality

Usage:
    python test_enhanced_ai_assistant.py
"""

import unittest
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta
import sys
import os

# Add the current directory to the path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from enhanced_options_ai_assistant import (
    EnhancedOptionsAIAssistant,
    OptionsPosition,
    PositionType,
    NewsCatalyst,
    CatalystImpact,
    OptionsAnalysisContext
)

class TestEnhancedOptionsAIAssistant(unittest.TestCase):
    """Test cases for the Enhanced Options AI Assistant"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.api_key = "test_api_key"
        self.assistant = EnhancedOptionsAIAssistant(self.api_key)
        
        # Create sample position
        self.sample_position = OptionsPosition(
            symbol="AAPL",
            position_type=PositionType.CASH_SECURED_PUT,
            entry_date=datetime.now() - timedelta(days=10),
            expiration_date=datetime.now() + timedelta(days=25),
            strike_price=220.0,
            entry_price=3.50,
            quantity=2,
            current_price=2.80,
            days_to_expiration=25,
            original_strategy_rationale="Test CSP position"
        )
        
        # Create sample news catalyst
        self.sample_catalyst = NewsCatalyst(
            headline="Apple Reports Strong Q4 Earnings",
            published_date=datetime.now() - timedelta(hours=2),
            catalyst_type="earnings",
            impact_level=CatalystImpact.HIGH,
            sentiment_score=0.7,
            relevance_score=0.9,
            summary="Apple exceeded earnings expectations with strong iPhone sales",
            source="Reuters"
        )
    
    def test_initialization(self):
        """Test assistant initialization"""
        self.assertIsNotNone(self.assistant)
        self.assertEqual(self.assistant.fmp_api_key, self.api_key)
        self.assertIsInstance(self.assistant.portfolio_positions, dict)
    
    def test_add_portfolio_position(self):
        """Test adding positions to portfolio"""
        self.assistant.add_portfolio_position(self.sample_position)
        
        self.assertIn("AAPL", self.assistant.portfolio_positions)
        self.assertEqual(len(self.assistant.portfolio_positions["AAPL"]), 1)
        self.assertEqual(self.assistant.portfolio_positions["AAPL"][0], self.sample_position)
    
    def test_classify_news_catalyst(self):
        """Test news catalyst classification"""
        # Test earnings catalyst
        catalyst_type, impact = self.assistant._classify_news_catalyst(
            "Apple beats earnings expectations", 
            "Strong quarterly results"
        )
        self.assertEqual(catalyst_type, "earnings")
        self.assertEqual(impact, CatalystImpact.HIGH)
        
        # Test analyst rating catalyst
        catalyst_type, impact = self.assistant._classify_news_catalyst(
            "Morgan Stanley upgrades AAPL to Buy", 
            "Price target raised to $250"
        )
        self.assertEqual(catalyst_type, "analyst_rating")
        self.assertEqual(impact, CatalystImpact.HIGH)
        
        # Test general news
        catalyst_type, impact = self.assistant._classify_news_catalyst(
            "Apple announces new product", 
            "Minor product update"
        )
        self.assertEqual(catalyst_type, "business_development")
        self.assertEqual(impact, CatalystImpact.MEDIUM)
    
    def test_calculate_sentiment_score(self):
        """Test sentiment score calculation"""
        # Positive sentiment
        score = self.assistant._calculate_sentiment_score(
            "Strong earnings beat", 
            "Excellent growth and positive outlook"
        )
        self.assertGreater(score, 0)
        
        # Negative sentiment
        score = self.assistant._calculate_sentiment_score(
            "Earnings miss disappointing", 
            "Weak performance and negative outlook"
        )
        self.assertLess(score, 0)
        
        # Neutral sentiment
        score = self.assistant._calculate_sentiment_score(
            "Company announces update", 
            "Regular business update"
        )
        self.assertEqual(score, 0.0)
    
    def test_calculate_relevance_score(self):
        """Test relevance score calculation"""
        # Direct symbol mention
        score = self.assistant._calculate_relevance_score(
            "AAPL reports earnings", 
            "Apple quarterly results", 
            "AAPL"
        )
        self.assertEqual(score, 1.0)
        
        # Company name mention
        score = self.assistant._calculate_relevance_score(
            "Apple iPhone sales strong", 
            "iPhone revenue growth", 
            "AAPL"
        )
        self.assertEqual(score, 0.8)
        
        # Sector mention
        score = self.assistant._calculate_relevance_score(
            "Tech sector outlook", 
            "Technology stocks analysis", 
            "AAPL"
        )
        self.assertEqual(score, 0.4)
    
    def test_assess_assignment_risk(self):
        """Test assignment risk assessment"""
        # Create context with current price
        context = Mock()
        context.current_price = 225.0  # Above strike price
        
        risk = self.assistant._assess_assignment_risk(self.sample_position, context)
        self.assertEqual(risk, "low")
        
        # Test high risk scenario
        context.current_price = 218.0  # Below strike price
        risk = self.assistant._assess_assignment_risk(self.sample_position, context)
        self.assertEqual(risk, "high")
    
    def test_calculate_position_pnl(self):
        """Test position P&L calculation"""
        context = Mock()
        context.current_price = 225.0
        
        pnl = self.assistant._calculate_position_pnl(self.sample_position, context)
        
        self.assertIn('unrealized_pnl', pnl)
        self.assertIn('percent_return', pnl)
        
        # For CSP, profit when option price decreases
        expected_pnl = (3.50 - 2.80) * 2 * 100  # $140 profit
        self.assertEqual(pnl['unrealized_pnl'], expected_pnl)
    
    def test_iv_environment_assessment(self):
        """Test IV environment assessment"""
        # High IV
        assessment = self.assistant._assess_iv_environment(0.85, 0.1)
        self.assertIn("Extremely high IV", assessment)
        
        # Low IV
        assessment = self.assistant._assess_iv_environment(0.15, -0.05)
        self.assertIn("Very low IV", assessment)
        
        # Moderate IV
        assessment = self.assistant._assess_iv_environment(0.45, 0.02)
        self.assertIn("Moderate IV", assessment)
    
    def test_volume_environment_assessment(self):
        """Test volume environment assessment"""
        # High volume, bearish flow
        assessment = self.assistant._assess_volume_environment(2.5, 1.8)
        self.assertIn("High volume", assessment)
        self.assertIn("Heavy put buying", assessment)
        
        # Low volume, bullish flow
        assessment = self.assistant._assess_volume_environment(0.3, 0.4)
        self.assertIn("Low volume", assessment)
        self.assertIn("Heavy call buying", assessment)
    
    def test_portfolio_risk_analysis(self):
        """Test portfolio risk analysis"""
        # Add multiple positions
        positions = [
            self.sample_position,
            OptionsPosition(
                symbol="NVDA",
                position_type=PositionType.CASH_SECURED_PUT,
                entry_date=datetime.now() - timedelta(days=5),
                expiration_date=datetime.now() + timedelta(days=5),  # Near expiration
                strike_price=130.0,
                entry_price=4.25,
                quantity=1,
                days_to_expiration=5
            )
        ]
        
        for position in positions:
            self.assistant.add_portfolio_position(position)
        
        risk_analysis = self.assistant.analyze_portfolio_risk()
        
        self.assertEqual(risk_analysis['total_positions'], 2)
        self.assertEqual(risk_analysis['symbols_count'], 2)
        self.assertEqual(risk_analysis['near_expiration_positions'], 1)
        self.assertIn('cash_secured_put', risk_analysis['position_types'])
    
    def test_get_portfolio_summary(self):
        """Test portfolio summary generation"""
        self.assistant.add_portfolio_position(self.sample_position)
        
        summary = self.assistant.get_portfolio_summary()
        
        self.assertIn("PORTFOLIO SUMMARY", summary)
        self.assertIn("AAPL", summary)
        self.assertIn("cash_secured_put", summary)
        self.assertIn("$220.00", summary)
    
    @patch('enhanced_options_ai_assistant.get_options_chain_yf')
    @patch('enhanced_options_ai_assistant.fmp_news_today')
    def test_build_analysis_context(self, mock_news, mock_options):
        """Test building analysis context"""
        # Mock data
        mock_news.return_value = [
            {
                'title': 'Apple beats earnings',
                'text': 'Strong quarterly results',
                'publishedDate': '2024-01-15T10:00:00Z',
                'site': 'Reuters'
            }
        ]
        
        mock_options.return_value = [
            {
                'type': 'put',
                'strike': 220.0,
                'volume': 1000,
                'openInterest': 5000
            }
        ]
        
        # Mock live data provider
        mock_live_data = Mock()
        mock_live_data.current_price = 225.0
        mock_live_data.change_percent = 2.5
        mock_live_data.volume = 50000000
        mock_live_data.avg_volume = 40000000
        mock_live_data.iv_rank = 0.65
        mock_live_data.iv_percentile = 0.70
        mock_live_data.hv_30 = 0.25
        mock_live_data.iv_30 = 0.30
        mock_live_data.support_level = 220.0
        mock_live_data.resistance_level = 230.0
        mock_live_data.earnings_date = None
        mock_live_data.earnings_days_away = None
        mock_live_data.expected_move = None
        
        self.assistant.market_data_provider.get_live_data = Mock(return_value=mock_live_data)
        
        context = self.assistant._build_analysis_context("AAPL")
        
        self.assertIsInstance(context, OptionsAnalysisContext)
        self.assertEqual(context.symbol, "AAPL")
        self.assertEqual(context.current_price, 225.0)
        self.assertGreater(len(context.news_catalysts), 0)

class TestIntegrationScenarios(unittest.TestCase):
    """Test real-world integration scenarios"""
    
    def setUp(self):
        """Set up integration test fixtures"""
        self.api_key = "test_api_key"
        self.assistant = EnhancedOptionsAIAssistant(self.api_key)
    
    def test_csp_catalyst_analysis_workflow(self):
        """Test complete CSP catalyst analysis workflow"""
        # This would be a more comprehensive integration test
        # For now, we'll test the method exists and handles errors gracefully
        
        try:
            # This will fail due to API calls, but should handle gracefully
            result = self.assistant.analyze_cash_secured_put_catalyst("AAPL")
            # If we get here, the method completed (possibly with mock data)
            self.assertIsInstance(result, str)
        except Exception as e:
            # Expected to fail in test environment without real API
            self.assertIsInstance(e, Exception)

def run_comprehensive_tests():
    """Run all test suites"""
    print("🧪 Running Enhanced Options AI Assistant Test Suite")
    print("=" * 60)
    
    # Create test suite
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Add test cases
    suite.addTests(loader.loadTestsFromTestCase(TestEnhancedOptionsAIAssistant))
    suite.addTests(loader.loadTestsFromTestCase(TestIntegrationScenarios))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Print summary
    print("\n" + "=" * 60)
    if result.wasSuccessful():
        print("✅ All tests passed successfully!")
    else:
        print(f"❌ {len(result.failures)} test(s) failed, {len(result.errors)} error(s)")
        
        if result.failures:
            print("\nFailures:")
            for test, traceback in result.failures:
                print(f"  - {test}: {traceback}")
        
        if result.errors:
            print("\nErrors:")
            for test, traceback in result.errors:
                print(f"  - {test}: {traceback}")
    
    print("=" * 60)
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_comprehensive_tests()
    sys.exit(0 if success else 1)
