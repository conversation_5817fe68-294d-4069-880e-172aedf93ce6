"""
Best Strategy Dashboard Section
Comprehensive strategy display for each stock with AI-powered analysis

This module creates a detailed "Best Strategy Section" that displays:
1. Primary strategy recommendation with execution details
2. Strategy justification with <PERSON>'s criteria alignment
3. AI-powered financial analyst reviews
4. News impact analysis with sentiment scoring
5. Supporting catalysts and real-time validation
6. Color-coded, scannable desktop interface

Date: August 18, 2025
"""

import tkinter as tk
from tkinter import ttk, scrolledtext
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from datetime import datetime, timedelta
from enum import Enum
import threading

from intelligent_strategy_engine import StrategyOfTheDay, IntelligentStrategyEngine
from strategy_criteria_display import StrategyCriteriaAnalyzer, StrategyCriteriaAnalysis, CriteriaStatus
from ai_analysis_engine import AIAnalysisEngine
from daily_outline import StrategyType

class ConfidenceLevel(Enum):
    STRONG = "STRONG"
    MODERATE = "MODERATE"
    WEAK = "WEAK"
    AVOID = "AVOID"

@dataclass
class FinancialAnalysis:
    """AI-generated financial analysis for a stock"""
    symbol: str
    fundamental_summary: str
    technical_outlook: str
    analyst_price_target: float
    consensus_rating: str
    pe_ratio: float
    growth_rate: float
    financial_health_score: float
    support_levels: List[float]
    resistance_levels: List[float]
    confidence_interval_low: float
    confidence_interval_high: float
    timeframe_days: int

@dataclass
class NewsImpactAnalysis:
    """News impact analysis for strategy validation"""
    symbol: str
    recent_articles: List[Dict]
    overall_sentiment: float  # -1 to 1
    sentiment_summary: str
    strategy_support_score: float  # How much news supports the strategy
    key_catalysts: List[str]
    risk_factors: List[str]
    earnings_impact: str
    regulatory_impact: str

@dataclass
class SupportingCatalysts:
    """Supporting catalysts for the strategy"""
    symbol: str
    upcoming_events: List[Tuple[datetime, str, str]]  # date, event, impact
    sector_rotation_support: float
    technical_catalysts: List[str]
    volatility_catalysts: List[str]
    seasonal_patterns: List[str]
    fed_meeting_impact: str

@dataclass
class BestStrategyRecommendation:
    """Complete best strategy recommendation with all analysis"""
    symbol: str
    strategy: StrategyOfTheDay
    criteria_analysis: StrategyCriteriaAnalysis
    financial_analysis: FinancialAnalysis
    news_analysis: NewsImpactAnalysis
    catalysts: SupportingCatalysts
    execution_details: Dict[str, Any]
    last_updated: datetime

class BestStrategyWidget(ttk.Frame):
    """Widget displaying the best strategy for a single stock"""

    def __init__(self, parent, symbol: str, **kwargs):
        super().__init__(parent, **kwargs)

        self.symbol = symbol
        self.current_recommendation: Optional[BestStrategyRecommendation] = None

        # Analysis components
        self.criteria_analyzer = StrategyCriteriaAnalyzer()
        self.ai_analyzer: Optional[AIAnalysisEngine] = None

        self.setup_widget()

    def setup_widget(self):
        """Setup the best strategy widget layout"""

        # Main container with symbol header
        header_frame = ttk.Frame(self)
        header_frame.pack(fill=tk.X, padx=5, pady=5)

        # Symbol and status
        symbol_label = ttk.Label(header_frame, text=self.symbol,
                                font=('Arial', 14, 'bold'))
        symbol_label.pack(side=tk.LEFT)

        self.status_label = ttk.Label(header_frame, text="Loading...",
                                     font=('Arial', 10))
        self.status_label.pack(side=tk.RIGHT)

        # Create notebook for different sections
        self.notebook = ttk.Notebook(self)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Primary Strategy Tab
        self.strategy_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.strategy_frame, text="Primary Strategy")
        self._create_primary_strategy_section()

        # Justification Tab
        self.justification_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.justification_frame, text="Justification")
        self._create_justification_section()

        # Financial Analysis Tab
        self.financial_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.financial_frame, text="Financial Analysis")
        self._create_financial_analysis_section()

        # News Impact Tab
        self.news_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.news_frame, text="News Impact")
        self._create_news_impact_section()

        # Catalysts Tab
        self.catalysts_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.catalysts_frame, text="Catalysts")
        self._create_catalysts_section()

    def _create_primary_strategy_section(self):
        """Create primary strategy recommendation section"""

        # Strategy header with confidence
        strategy_header = ttk.LabelFrame(self.strategy_frame, text="Recommended Strategy")
        strategy_header.pack(fill=tk.X, padx=5, pady=5)

        # Strategy name and confidence
        strategy_info_frame = ttk.Frame(strategy_header)
        strategy_info_frame.pack(fill=tk.X, padx=5, pady=5)

        self.strategy_name_label = ttk.Label(strategy_info_frame, text="--",
                                           font=('Arial', 16, 'bold'))
        self.strategy_name_label.pack(side=tk.LEFT)

        self.confidence_label = ttk.Label(strategy_info_frame, text="--",
                                        font=('Arial', 12, 'bold'))
        self.confidence_label.pack(side=tk.RIGHT)

        # Confidence progress bar
        confidence_frame = ttk.Frame(strategy_header)
        confidence_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(confidence_frame, text="Confidence:").pack(side=tk.LEFT)
        self.confidence_bar = ttk.Progressbar(confidence_frame, length=200, mode='determinate')
        self.confidence_bar.pack(side=tk.LEFT, padx=(5, 0))

        self.strength_label = ttk.Label(confidence_frame, text="--",
                                      font=('Arial', 10, 'bold'))
        self.strength_label.pack(side=tk.LEFT, padx=(10, 0))

        # Execution details
        execution_frame = ttk.LabelFrame(self.strategy_frame, text="Execution Details")
        execution_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create execution details grid
        self.execution_labels = {}
        execution_items = [
            ("Strike Price", "strike_price"),
            ("Expiration", "expiration"),
            ("Position Size", "position_size"),
            ("Entry Price", "entry_price"),
            ("Target Price", "target_price"),
            ("Stop Loss", "stop_loss"),
            ("Max Risk", "max_risk"),
            ("Max Profit", "max_profit")
        ]

        for i, (label, key) in enumerate(execution_items):
            row = i // 2
            col = (i % 2) * 2

            ttk.Label(execution_frame, text=f"{label}:").grid(row=row, column=col,
                                                            sticky='w', padx=5, pady=2)
            self.execution_labels[key] = ttk.Label(execution_frame, text="--",
                                                 font=('Arial', 9, 'bold'))
            self.execution_labels[key].grid(row=row, column=col+1, sticky='w', padx=5, pady=2)

    def _create_justification_section(self):
        """Create strategy justification section"""

        # Top reasons
        reasons_frame = ttk.LabelFrame(self.justification_frame, text="Top Reasons for Selection")
        reasons_frame.pack(fill=tk.X, padx=5, pady=5)

        self.reasons_text = tk.Text(reasons_frame, height=6, font=('Arial', 10), wrap=tk.WORD)
        self.reasons_text.pack(fill=tk.X, padx=5, pady=5)

        # Criteria scores
        scores_frame = ttk.LabelFrame(self.justification_frame, text="Criteria Scores")
        scores_frame.pack(fill=tk.X, padx=5, pady=5)

        # Score bars
        self.score_bars = {}
        score_categories = [
            ("Market Environment", "market_score"),
            ("Stock Factors", "stock_score"),
            ("Erica's Criteria", "erica_score"),
            ("Risk Management", "risk_score")
        ]

        for i, (label, key) in enumerate(score_categories):
            score_frame = ttk.Frame(scores_frame)
            score_frame.pack(fill=tk.X, padx=5, pady=2)

            ttk.Label(score_frame, text=label, width=20).pack(side=tk.LEFT)

            progress_bar = ttk.Progressbar(score_frame, length=150, mode='determinate')
            progress_bar.pack(side=tk.LEFT, padx=(5, 0))

            score_label = ttk.Label(score_frame, text="--", font=('Arial', 9, 'bold'))
            score_label.pack(side=tk.LEFT, padx=(5, 0))

            self.score_bars[key] = (progress_bar, score_label)

        # Risk factors
        risk_frame = ttk.LabelFrame(self.justification_frame, text="Risk Factors & Mitigation")
        risk_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.risk_text = scrolledtext.ScrolledText(risk_frame, height=8, font=('Arial', 10), wrap=tk.WORD)
        self.risk_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

    def _create_financial_analysis_section(self):
        """Create AI-powered financial analysis section"""

        # Fundamental analysis
        fundamental_frame = ttk.LabelFrame(self.financial_frame, text="Fundamental Analysis")
        fundamental_frame.pack(fill=tk.X, padx=5, pady=5)

        self.fundamental_text = tk.Text(fundamental_frame, height=6, font=('Arial', 10), wrap=tk.WORD)
        self.fundamental_text.pack(fill=tk.X, padx=5, pady=5)

        # Key metrics
        metrics_frame = ttk.LabelFrame(self.financial_frame, text="Key Metrics")
        metrics_frame.pack(fill=tk.X, padx=5, pady=5)

        self.metrics_labels = {}
        metrics = [
            ("P/E Ratio", "pe_ratio"),
            ("Growth Rate", "growth_rate"),
            ("Financial Health", "health_score"),
            ("Analyst Target", "price_target"),
            ("Consensus Rating", "consensus")
        ]

        for i, (label, key) in enumerate(metrics):
            row = i // 3
            col = (i % 3) * 2

            ttk.Label(metrics_frame, text=f"{label}:").grid(row=row, column=col,
                                                          sticky='w', padx=5, pady=2)
            self.metrics_labels[key] = ttk.Label(metrics_frame, text="--",
                                               font=('Arial', 9, 'bold'))
            self.metrics_labels[key].grid(row=row, column=col+1, sticky='w', padx=5, pady=2)

        # Technical outlook
        technical_frame = ttk.LabelFrame(self.financial_frame, text="Technical Outlook")
        technical_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.technical_text = scrolledtext.ScrolledText(technical_frame, height=8, font=('Arial', 10), wrap=tk.WORD)
        self.technical_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

    def _create_news_impact_section(self):
        """Create news impact analysis section"""

        # Sentiment overview
        sentiment_frame = ttk.LabelFrame(self.news_frame, text="News Sentiment Analysis")
        sentiment_frame.pack(fill=tk.X, padx=5, pady=5)

        # Sentiment bar
        sentiment_bar_frame = ttk.Frame(sentiment_frame)
        sentiment_bar_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(sentiment_bar_frame, text="Overall Sentiment:").pack(side=tk.LEFT)
        self.sentiment_bar = ttk.Progressbar(sentiment_bar_frame, length=200, mode='determinate')
        self.sentiment_bar.pack(side=tk.LEFT, padx=(5, 0))

        self.sentiment_label = ttk.Label(sentiment_bar_frame, text="--",
                                       font=('Arial', 10, 'bold'))
        self.sentiment_label.pack(side=tk.LEFT, padx=(10, 0))

        # Strategy support score
        support_frame = ttk.Frame(sentiment_frame)
        support_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(support_frame, text="Strategy Support:").pack(side=tk.LEFT)
        self.support_bar = ttk.Progressbar(support_frame, length=200, mode='determinate')
        self.support_bar.pack(side=tk.LEFT, padx=(5, 0))

        self.support_label = ttk.Label(support_frame, text="--",
                                     font=('Arial', 10, 'bold'))
        self.support_label.pack(side=tk.LEFT, padx=(10, 0))

        # News summary
        news_summary_frame = ttk.LabelFrame(self.news_frame, text="News Impact Summary")
        news_summary_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.news_summary_text = scrolledtext.ScrolledText(news_summary_frame, height=12,
                                                          font=('Arial', 10), wrap=tk.WORD)
        self.news_summary_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

    def _create_catalysts_section(self):
        """Create supporting catalysts section"""

        # Upcoming events
        events_frame = ttk.LabelFrame(self.catalysts_frame, text="Upcoming Events")
        events_frame.pack(fill=tk.X, padx=5, pady=5)

        # Events treeview
        columns = ('Date', 'Event', 'Impact')
        self.events_tree = ttk.Treeview(events_frame, columns=columns, show='headings', height=6)

        for col in columns:
            self.events_tree.heading(col, text=col)
            self.events_tree.column(col, width=120)

        self.events_tree.pack(fill=tk.X, padx=5, pady=5)

        # Technical catalysts
        tech_catalysts_frame = ttk.LabelFrame(self.catalysts_frame, text="Technical Catalysts")
        tech_catalysts_frame.pack(fill=tk.X, padx=5, pady=5)

        self.tech_catalysts_text = tk.Text(tech_catalysts_frame, height=4,
                                          font=('Arial', 10), wrap=tk.WORD)
        self.tech_catalysts_text.pack(fill=tk.X, padx=5, pady=5)

        # Sector rotation support
        sector_frame = ttk.LabelFrame(self.catalysts_frame, text="Sector Analysis")
        sector_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.sector_text = scrolledtext.ScrolledText(sector_frame, height=6,
                                                    font=('Arial', 10), wrap=tk.WORD)
        self.sector_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

    def update_recommendation(self, recommendation: BestStrategyRecommendation):
        """Update the widget with new recommendation data"""
        self.current_recommendation = recommendation

        # Update primary strategy section
        self._update_primary_strategy(recommendation)

        # Update justification section
        self._update_justification(recommendation)

        # Update financial analysis
        self._update_financial_analysis(recommendation)

        # Update news impact
        self._update_news_impact(recommendation)

        # Update catalysts
        self._update_catalysts(recommendation)

        # Update status
        self.status_label.config(text=f"Updated: {recommendation.last_updated.strftime('%H:%M:%S')}")

    def _update_primary_strategy(self, recommendation: BestStrategyRecommendation):
        """Update primary strategy section"""
        strategy = recommendation.strategy

        # Strategy name
        strategy_name = strategy.recommended_strategy.value.replace('_', ' ').title()
        self.strategy_name_label.config(text=strategy_name)

        # Confidence
        confidence_pct = strategy.confidence * 100
        self.confidence_label.config(text=f"{confidence_pct:.0f}%")
        self.confidence_bar['value'] = confidence_pct

        # Strength
        if confidence_pct >= 80:
            strength = "STRONG"
            strength_color = "green"
        elif confidence_pct >= 60:
            strength = "MODERATE"
            strength_color = "blue"
        elif confidence_pct >= 40:
            strength = "WEAK"
            strength_color = "orange"
        else:
            strength = "AVOID"
            strength_color = "red"

        self.strength_label.config(text=strength, foreground=strength_color)

        # Execution details
        execution = recommendation.execution_details
        self.execution_labels["strike_price"].config(text=f"${execution.get('strike_price', '--')}")
        self.execution_labels["expiration"].config(text=execution.get('expiration', '--'))
        self.execution_labels["position_size"].config(text=execution.get('position_size', '--'))
        self.execution_labels["entry_price"].config(text=f"${execution.get('entry_price', '--')}")
        self.execution_labels["target_price"].config(text=f"${execution.get('target_price', '--')}")
        self.execution_labels["stop_loss"].config(text=f"${execution.get('stop_loss', '--')}")
        self.execution_labels["max_risk"].config(text=f"${execution.get('max_risk', '--')}")
        self.execution_labels["max_profit"].config(text=f"${execution.get('max_profit', '--')}")

    def _update_justification(self, recommendation: BestStrategyRecommendation):
        """Update justification section"""
        criteria = recommendation.criteria_analysis

        # Top reasons
        self.reasons_text.delete(1.0, tk.END)
        reasons = [
            f"• {criteria.summary}",
            f"• Overall criteria score: {criteria.overall_score:.0%}",
            f"• Strategy strength: {criteria.recommendation_strength}"
        ]

        # Add specific met criteria
        met_criteria = [c for c in criteria.criteria_items if c.status == CriteriaStatus.MET]
        for criteria_item in met_criteria[:3]:  # Top 3 met criteria
            reasons.append(f"• {criteria_item.name}: {criteria_item.description}")

        self.reasons_text.insert(tk.END, "\n".join(reasons))

        # Update score bars
        category_scores = {
            "market_score": sum(c.score for c in criteria.market_criteria) / len(criteria.market_criteria) if criteria.market_criteria else 0,
            "stock_score": sum(c.score for c in criteria.stock_criteria) / len(criteria.stock_criteria) if criteria.stock_criteria else 0,
            "erica_score": sum(c.score for c in criteria.erica_criteria) / len(criteria.erica_criteria) if criteria.erica_criteria else 0,
            "risk_score": sum(c.score for c in criteria.risk_criteria) / len(criteria.risk_criteria) if criteria.risk_criteria else 0
        }

        for key, score in category_scores.items():
            if key in self.score_bars:
                progress_bar, score_label = self.score_bars[key]
                progress_bar['value'] = score * 100
                score_label.config(text=f"{score:.0%}")

        # Risk factors
        self.risk_text.delete(1.0, tk.END)
        risk_content = "Risk Factors:\n"

        # Add risk factors from criteria
        risk_factors = []
        for criteria_item in criteria.criteria_items:
            risk_factors.extend(criteria_item.risk_factors)

        for risk in set(risk_factors[:5]):  # Top 5 unique risks
            risk_content += f"• {risk}\n"

        risk_content += "\nMitigation Strategies:\n"
        risk_content += "• Position sizing based on volatility\n"
        risk_content += "• Stop-loss levels defined\n"
        risk_content += "• Regular monitoring and adjustment\n"
        risk_content += "• Diversification across strategies\n"

        self.risk_text.insert(tk.END, risk_content)

    def _update_financial_analysis(self, recommendation: BestStrategyRecommendation):
        """Update financial analysis section"""
        financial = recommendation.financial_analysis

        # Fundamental analysis
        self.fundamental_text.delete(1.0, tk.END)
        self.fundamental_text.insert(tk.END, financial.fundamental_summary)

        # Key metrics
        self.metrics_labels["pe_ratio"].config(text=f"{financial.pe_ratio:.1f}")
        self.metrics_labels["growth_rate"].config(text=f"{financial.growth_rate:.1%}")
        self.metrics_labels["health_score"].config(text=f"{financial.financial_health_score:.0%}")
        self.metrics_labels["price_target"].config(text=f"${financial.analyst_price_target:.2f}")
        self.metrics_labels["consensus"].config(text=financial.consensus_rating)

        # Technical outlook
        self.technical_text.delete(1.0, tk.END)

        technical_content = f"{financial.technical_outlook}\n\n"
        technical_content += f"Support Levels: {', '.join([f'${level:.2f}' for level in financial.support_levels])}\n"
        technical_content += f"Resistance Levels: {', '.join([f'${level:.2f}' for level in financial.resistance_levels])}\n\n"
        technical_content += f"Price Confidence Interval ({financial.timeframe_days} days):\n"
        technical_content += f"Low: ${financial.confidence_interval_low:.2f}\n"
        technical_content += f"High: ${financial.confidence_interval_high:.2f}"

        self.technical_text.insert(tk.END, technical_content)

    def _update_news_impact(self, recommendation: BestStrategyRecommendation):
        """Update news impact section"""
        news = recommendation.news_analysis

        # Sentiment bars
        sentiment_pct = (news.overall_sentiment + 1) * 50  # Convert -1,1 to 0,100
        self.sentiment_bar['value'] = sentiment_pct

        if sentiment_pct >= 60:
            sentiment_text = "Bullish"
            sentiment_color = "green"
        elif sentiment_pct >= 40:
            sentiment_text = "Neutral"
            sentiment_color = "blue"
        else:
            sentiment_text = "Bearish"
            sentiment_color = "red"

        self.sentiment_label.config(text=sentiment_text, foreground=sentiment_color)

        # Strategy support
        support_pct = news.strategy_support_score * 100
        self.support_bar['value'] = support_pct

        if support_pct >= 70:
            support_text = "Strong Support"
            support_color = "green"
        elif support_pct >= 50:
            support_text = "Moderate Support"
            support_color = "blue"
        else:
            support_text = "Weak Support"
            support_color = "orange"

        self.support_label.config(text=support_text, foreground=support_color)

        # News summary
        self.news_summary_text.delete(1.0, tk.END)

        summary_content = f"{news.sentiment_summary}\n\n"
        summary_content += "Key Catalysts:\n"
        for catalyst in news.key_catalysts:
            summary_content += f"• {catalyst}\n"

        summary_content += "\nRisk Factors:\n"
        for risk in news.risk_factors:
            summary_content += f"• {risk}\n"

        if news.earnings_impact:
            summary_content += f"\nEarnings Impact: {news.earnings_impact}\n"

        if news.regulatory_impact:
            summary_content += f"Regulatory Impact: {news.regulatory_impact}\n"

        self.news_summary_text.insert(tk.END, summary_content)

    def _update_catalysts(self, recommendation: BestStrategyRecommendation):
        """Update catalysts section"""
        catalysts = recommendation.catalysts

        # Clear events tree
        for item in self.events_tree.get_children():
            self.events_tree.delete(item)

        # Add upcoming events
        for date, event, impact in catalysts.upcoming_events:
            self.events_tree.insert('', 'end', values=(
                date.strftime('%m/%d'),
                event,
                impact
            ))

        # Technical catalysts
        self.tech_catalysts_text.delete(1.0, tk.END)
        tech_content = "\n".join([f"• {catalyst}" for catalyst in catalysts.technical_catalysts])
        self.tech_catalysts_text.insert(tk.END, tech_content)

        # Sector analysis
        self.sector_text.delete(1.0, tk.END)

        sector_content = f"Sector Rotation Support: {catalysts.sector_rotation_support:.0%}\n\n"

        if catalysts.volatility_catalysts:
            sector_content += "Volatility Catalysts:\n"
            for catalyst in catalysts.volatility_catalysts:
                sector_content += f"• {catalyst}\n"
            sector_content += "\n"

        if catalysts.seasonal_patterns:
            sector_content += "Seasonal Patterns:\n"
            for pattern in catalysts.seasonal_patterns:
                sector_content += f"• {pattern}\n"
            sector_content += "\n"

        if catalysts.fed_meeting_impact:
            sector_content += f"Fed Meeting Impact: {catalysts.fed_meeting_impact}"

        self.sector_text.insert(tk.END, sector_content)


class BestStrategyDashboard(ttk.Frame):
    """Main dashboard containing best strategy widgets for all stocks"""

    def __init__(self, parent, api_key: str, symbols: List[str] = None, **kwargs):
        super().__init__(parent, **kwargs)

        self.api_key = api_key
        self.symbols = symbols or ["AAPL", "NVDA", "AMD", "GOOGL", "AMZN"]

        # Analysis components
        self.strategy_engine = IntelligentStrategyEngine(api_key)
        self.ai_analyzer = AIAnalysisEngine(api_key)
        self.criteria_analyzer = StrategyCriteriaAnalyzer()

        # Widget storage
        self.strategy_widgets: Dict[str, BestStrategyWidget] = {}
        self.current_recommendations: Dict[str, BestStrategyRecommendation] = {}

        # Auto-refresh
        self.auto_refresh_active = False
        self.refresh_interval = 300  # 5 minutes

        self.setup_dashboard()

    def setup_dashboard(self):
        """Setup the main dashboard layout"""

        # Header with controls
        header_frame = ttk.Frame(self)
        header_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(header_frame, text="Best Strategy Dashboard",
                 font=('Arial', 16, 'bold')).pack(side=tk.LEFT)

        # Controls
        controls_frame = ttk.Frame(header_frame)
        controls_frame.pack(side=tk.RIGHT)

        ttk.Button(controls_frame, text="Refresh All",
                  command=self.refresh_all_strategies).pack(side=tk.LEFT, padx=2)

        ttk.Button(controls_frame, text="Export Report",
                  command=self.export_dashboard_report).pack(side=tk.LEFT, padx=2)

        # Auto-refresh toggle
        self.auto_refresh_var = tk.BooleanVar()
        ttk.Checkbutton(controls_frame, text="Auto Refresh",
                       variable=self.auto_refresh_var,
                       command=self.toggle_auto_refresh).pack(side=tk.LEFT, padx=5)

        # Last update time
        self.last_update_label = ttk.Label(controls_frame, text="Never updated",
                                         font=('Arial', 9))
        self.last_update_label.pack(side=tk.LEFT, padx=5)

        # Create notebook for symbols
        self.symbol_notebook = ttk.Notebook(self)
        self.symbol_notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create widget for each symbol
        for symbol in self.symbols:
            widget = BestStrategyWidget(self.symbol_notebook, symbol)
            widget.ai_analyzer = self.ai_analyzer  # Share AI analyzer
            self.strategy_widgets[symbol] = widget
            self.symbol_notebook.add(widget, text=symbol)

        # Refresh selected tab on change to avoid stale data
        self.symbol_notebook.bind("<<NotebookTabChanged>>", self._on_symbol_tab_changed)

    def refresh_all_strategies(self):
        """Refresh strategy recommendations for all symbols"""

        def refresh_thread():
            try:
                # Update last update time
                self.last_update_label.config(text="Updating...")

                # Generate new recommendations
                market_report, strategies = self.strategy_engine.generate_daily_recommendations(self.symbols)

                # Process each strategy
                for strategy in strategies:
                    recommendation = self._generate_comprehensive_recommendation(strategy, market_report)
                    self.current_recommendations[strategy.symbol] = recommendation

                    # Update widget on main thread
                    self.after(0, lambda s=strategy.symbol, r=recommendation:
                             self.strategy_widgets[s].update_recommendation(r))

                # Update timestamp
                update_time = datetime.now().strftime('%H:%M:%S')
                self.after(0, lambda: self.last_update_label.config(text=f"Updated: {update_time}"))

            except Exception as e:
                error_msg = str(e)[:20] + "..."
                self.after(0, lambda msg=error_msg: self.last_update_label.config(text=f"Error: {msg}"))

    def _on_symbol_tab_changed(self, event):
        """Refresh the currently selected symbol's widget to ensure fresh, per-symbol data"""
        try:
            selected_tab = event.widget.select()
            widget = event.widget.nametowidget(selected_tab)
            # Identify symbol from our mapping
            symbol = None
            for sym, wid in self.strategy_widgets.items():
                if wid is widget:
                    symbol = sym
                    break
            if not symbol:
                return

            # If we already have a recent recommendation, skip heavy recompute unless stale
            rec = self.current_recommendations.get(symbol)
            if rec and (datetime.now() - rec.last_updated).seconds < 120:
                widget.update_recommendation(rec)
                return

            # Otherwise, refresh just this symbol to avoid showing stale or generic info
            self.refresh_symbol_strategy(symbol)
        except Exception:
            pass

    def refresh_symbol_strategy(self, symbol: str):
        """Refresh strategy recommendation for a single symbol"""
        def worker():
            try:
                self.after(0, lambda: self.last_update_label.config(text=f"Updating {symbol}..."))
                market_report, strategies = self.strategy_engine.generate_daily_recommendations([symbol])
                if strategies:
                    strategy = strategies[0]
                    recommendation = self._generate_comprehensive_recommendation(strategy, market_report)
                    self.current_recommendations[symbol] = recommendation
                    self.after(0, lambda s=symbol, r=recommendation: self.strategy_widgets[s].update_recommendation(r))
                    update_time = datetime.now().strftime('%H:%M:%S')
                    self.after(0, lambda: self.last_update_label.config(text=f"Updated: {update_time}"))
            except Exception as e:
                err = str(e)[:20] + "..."
                self.after(0, lambda msg=err: self.last_update_label.config(text=f"Error: {msg}"))
        threading.Thread(target=worker, daemon=True).start()


    def _generate_comprehensive_recommendation(self, strategy: StrategyOfTheDay,
                                            market_report) -> BestStrategyRecommendation:
        """Generate comprehensive recommendation with all analysis components"""

        # Get market and stock factors
        try:
            # Use the actual MarketFactors from the engine cache
            market_factors = self.strategy_engine._get_market_factors()
        except Exception:
            # Fallback: derive a minimal MarketFactors from analyzer if needed
            market_factors = self.strategy_engine._get_market_factors()
        stock_factors = self._get_stock_factors(strategy.symbol)

        # Generate criteria analysis
        criteria_analysis = self.criteria_analyzer.analyze_strategy_criteria(
            self._create_mock_strategy_rec(strategy), market_factors, stock_factors
        )

        # Generate AI-powered financial analysis
        financial_analysis = self._generate_financial_analysis(strategy.symbol)

        # Generate news impact analysis
        news_analysis = self._generate_news_analysis(strategy.symbol, strategy)

        # Generate supporting catalysts
        catalysts = self._generate_catalysts_analysis(strategy.symbol)

        # Calculate execution details
        execution_details = self._calculate_execution_details(strategy)

        return BestStrategyRecommendation(
            symbol=strategy.symbol,
            strategy=strategy,
            criteria_analysis=criteria_analysis,
            financial_analysis=financial_analysis,
            news_analysis=news_analysis,
            catalysts=catalysts,
            execution_details=execution_details,
            last_updated=datetime.now()
        )

    def _generate_financial_analysis(self, symbol: str) -> FinancialAnalysis:
        """Generate financial analysis using real per-symbol data when available"""

        try:
            # Pull per-symbol factors from the market analyzer for fundamentals context
            stock_factors = self.strategy_engine.market_analyzer.analyze_stock_factors(symbol)

            # Get a quick quote via EnhancedFMPClient for support/resistance approximation
            quotes = self.strategy_engine.market_analyzer.enhanced_client.get_real_time_quotes([symbol])
            quote = quotes.get(symbol, {}) if isinstance(quotes, dict) else {}
            price = float(quote.get('price', quote.get('previousClose', 0)) or 0)

            # Simple symbol-specific support/resistance using percent bands if TA unavailable here
            support_levels = [round(price * x, 2) for x in (0.97, 0.95, 0.92)] if price else []
            resistance_levels = [round(price * x, 2) for x in (1.03, 1.05, 1.08)] if price else []

            # Build summary from factors to make it symbol-specific
            tech_summary_bits = []
            if stock_factors.technical_confluence_score is not None:
                tech_summary_bits.append(
                    f"Technical confluence {stock_factors.technical_confluence_score:.0%} ({stock_factors.trend_alignment})"
                )
            if getattr(stock_factors, 'rsi', None) is not None:
                tech_summary_bits.append(f"RSI: {stock_factors.rsi:.1f}")
            if stock_factors.relative_strength_vs_spy is not None:
                tech_summary_bits.append(
                    f"RS vs SPY: {stock_factors.relative_strength_vs_spy:+.2f}"
                )
            technical_outlook = "; ".join(tech_summary_bits) or "Mixed technical outlook"

            # Pull fundamentals via FMP endpoints
            client = self.strategy_engine.market_analyzer.enhanced_client
            profile = client.get_company_profile(symbol) or {}
            rating = client.get_rating(symbol) or {}
            pt = client.get_price_target(symbol) or {}
            kmtm = client.get_key_metrics_ttm(symbol) or {}

            # Earnings/fundamental summary
            fundamental_summary = (
                f"News sentiment {stock_factors.news_sentiment_score:+.2f}; "
                f"Sector perf {stock_factors.sector_performance:+.2f}; "
                f"Earnings in {stock_factors.earnings_days_away or 'N/A'} days"
            )

            # Use FMP data where possible
            try:
                pe_ratio = float(profile.get('pe', profile.get('peRatio', 0.0)) or 0.0)
            except Exception:
                pe_ratio = 0.0

            # Growth proxy from key metrics (e.g., revenuePerShareTTM growth or other growth fields)
            growth_rate = 0.10
            for key in (
                'revenuePerShareTTMYoyGrowth', 'revenueGrowthTTMYoy', 'netIncomeGrowthTTMYoy',
                'revenueGrowthTTM', 'revenueGrowthYoY'
            ):
                val = kmtm.get(key)
                if isinstance(val, (int, float)):
                    growth_rate = float(val)
                    break

            # Health score from liquidity/solvency metrics if available
            quick_ratio = kmtm.get('quickRatioTTM') or kmtm.get('quickRatio')
            debt_eq = kmtm.get('debtToEquityTTM') or kmtm.get('debtToEquity')
            health_score = 0.7
            try:
                if isinstance(quick_ratio, (int, float)) and isinstance(debt_eq, (int, float)):
                    # Normalize: quick ratio > 1 good; lower debt/equity better
                    qr_score = max(0.0, min(1.0, (float(quick_ratio) / 2.0)))
                    de_score = max(0.0, min(1.0, (1.0 - min(float(debt_eq), 2.0) / 2.0)))
                    health_score = 0.5 * qr_score + 0.5 * de_score
            except Exception:
                pass

            analyst_price_target = 0.0
            for key in ('mean', 'targetMean', 'priceTargetAverage', 'priceTargetMean'):
                v = pt.get(key)
                if isinstance(v, (int, float)):
                    analyst_price_target = float(v)
                    break
            if not analyst_price_target and price:
                analyst_price_target = price * 1.05

            # Consensus rating mapping
            consensus_rating = rating.get('ratingRecommendation') or rating.get('rating') or ''
            if isinstance(consensus_rating, str) and consensus_rating:
                consensus_rating = consensus_rating.upper()
            else:
                consensus_rating = "HOLD"

            # Confidence interval from simple vol proxy (iv_rank) around price
            iv_rank = stock_factors.iv_rank or 0.5
            band = (0.05 + (iv_rank - 0.5) * 0.1)
            ci_low = round(price * (1 - band), 2) if price else 0.0
            ci_high = round(price * (1 + band), 2) if price else 0.0

            return FinancialAnalysis(
                symbol=symbol,
                fundamental_summary=fundamental_summary,
                technical_outlook=technical_outlook,
                analyst_price_target=float(analyst_price_target or 0.0),
                consensus_rating=consensus_rating,
                pe_ratio=float(pe_ratio),
                growth_rate=float(max(0.0, min(1.0, growth_rate))),
                financial_health_score=float(max(0.0, min(1.0, health_score))),
                support_levels=support_levels or [0.0],
                resistance_levels=resistance_levels or [0.0],
                confidence_interval_low=ci_low,
                confidence_interval_high=ci_high,
                timeframe_days=45
            )

        except Exception:
            # Fallback to per-symbol flavored defaults so tabs still differ
            return FinancialAnalysis(
                symbol=symbol,
                fundamental_summary=f"Fundamental snapshot for {symbol} unavailable; using defaults",
                technical_outlook=f"Mixed outlook; watch {symbol}-specific levels",
                analyst_price_target=0.0,
                consensus_rating="HOLD",
                pe_ratio=25.0,
                growth_rate=0.10,
                financial_health_score=0.70,
                support_levels=[],
                resistance_levels=[],
                confidence_interval_low=0.0,
                confidence_interval_high=0.0,
                timeframe_days=45
            )

    def _generate_news_analysis(self, symbol: str, strategy: StrategyOfTheDay) -> NewsImpactAnalysis:
        """Generate per-symbol news impact analysis using EnhancedFMPClient when possible"""

        try:
            # Pull latest news via EnhancedFMPClient for this symbol
            news_by_symbol = self.strategy_engine.market_analyzer.enhanced_client.get_news_sentiment([symbol], limit=15)
            articles = news_by_symbol.get(symbol, []) if isinstance(news_by_symbol, dict) else []

            # Build lightweight article list for UI and feed to AI analyzer for summary
            simple_articles = [
                {
                    "title": a.get("title"),
                    "content": a.get("title", ""),
                    "source": a.get("site"),
                    "date": a.get("published_date") or a.get("publishedDate")
                }
                for a in articles[:8]
            ]

            ai_analysis = self.ai_analyzer.analyze_news_sentiment(simple_articles, symbol)

            # Strategy support score derived from sentiment alignment with recommendation
            support = 0.7
            if ai_analysis.sentiment_score is not None:
                support = 0.5 + max(-0.5, min(0.5, ai_analysis.sentiment_score / 2))

            return NewsImpactAnalysis(
                symbol=symbol,
                recent_articles=simple_articles,
                overall_sentiment=float(ai_analysis.sentiment_score or 0.0),
                sentiment_summary=ai_analysis.plain_english_summary,
                strategy_support_score=float(max(0.0, min(1.0, support))),
                key_catalysts=ai_analysis.actionable_recommendations,
                risk_factors=ai_analysis.risk_factors,
                earnings_impact="",
                regulatory_impact=""
            )

        except Exception:
            # Fallback produces symbol-specific summary string so tabs differ
            return NewsImpactAnalysis(
                symbol=symbol,
                recent_articles=[],
                overall_sentiment=0.0,
                sentiment_summary=f"No recent articles found for {symbol}.",
                strategy_support_score=0.5,
                key_catalysts=[],
                risk_factors=[],
                earnings_impact="",
                regulatory_impact=""
            )

    def _generate_catalysts_analysis(self, symbol: str) -> SupportingCatalysts:
        """Generate supporting catalysts analysis"""

        # Mock upcoming events
        upcoming_events = [
            (datetime.now() + timedelta(days=7), "Earnings Call", "High Impact"),
            (datetime.now() + timedelta(days=14), "Product Launch", "Medium Impact"),
            (datetime.now() + timedelta(days=21), "Fed Meeting", "Market Impact"),
            (datetime.now() + timedelta(days=30), "Analyst Day", "Medium Impact")
        ]

        return SupportingCatalysts(
            symbol=symbol,
            upcoming_events=upcoming_events,
            sector_rotation_support=0.75,
            technical_catalysts=[
                "Breakout above 50-day moving average",
                "Volume surge on recent rallies",
                "RSI showing bullish divergence",
                "Support holding at key levels"
            ],
            volatility_catalysts=[
                "Options expiration approaching",
                "Earnings volatility expected",
                "Fed meeting uncertainty"
            ],
            seasonal_patterns=[
                "Strong Q4 historical performance",
                "Holiday season tailwinds",
                "Year-end portfolio rebalancing"
            ],
            fed_meeting_impact="Dovish stance expected to support growth stocks"
        )

    def _calculate_execution_details(self, strategy: StrategyOfTheDay) -> Dict[str, Any]:
        """Calculate detailed execution parameters"""

        # Mock execution details - in production would calculate based on real market data

        if strategy.recommended_strategy == StrategyType.COVERED_CALL:
            return {
                "strike_price": 160.0,
                "expiration": "Sep 15, 2025",
                "position_size": "2 contracts",
                "entry_price": 3.50,
                "target_price": 1.75,  # 50% profit target
                "stop_loss": 5.25,  # 150% of credit received
                "max_risk": 700.0,
                "max_profit": 350.0
            }
        elif strategy.recommended_strategy == StrategyType.CREDIT_SPREAD:
            return {
                "strike_price": "150/145 Put Spread",
                "expiration": "Sep 15, 2025",
                "position_size": "4 contracts",
                "entry_price": 1.65,
                "target_price": 0.83,  # 50% profit target
                "stop_loss": 3.30,  # 200% of credit received
                "max_risk": 1340.0,
                "max_profit": 660.0
            }
        elif strategy.recommended_strategy == StrategyType.LEAPS:
            return {
                "strike_price": 140.0,
                "expiration": "Jan 17, 2026",
                "position_size": "2 contracts",
                "entry_price": 25.00,
                "target_price": 35.00,  # 40% profit target
                "stop_loss": 17.50,  # 30% stop loss
                "max_risk": 5000.0,
                "max_profit": "Unlimited"
            }
        else:  # Premium selling
            return {
                "strike_price": 145.0,
                "expiration": "Sep 1, 2025",
                "position_size": "3 contracts",
                "entry_price": 2.25,
                "target_price": 1.13,  # 50% profit target
                "stop_loss": 4.50,  # 200% of credit received
                "max_risk": 1175.0,
                "max_profit": 675.0
            }

    def _get_stock_factors(self, symbol: str):
        """Get stock-specific factors using the engine's analyzer for per-symbol results"""
        try:
            return self.strategy_engine.market_analyzer.analyze_stock_factors(symbol)
        except Exception:
            # Fallback to ensure symbol-specific object exists
            from market_analysis_engine import StockSpecificFactors
            return StockSpecificFactors(
                symbol=symbol,
                news_sentiment_score=0.0,
                earnings_days_away=None
            )

    def _create_mock_strategy_rec(self, strategy: StrategyOfTheDay):
        """Create mock strategy recommendation for criteria analysis"""
        from strategy_decision_tree import StrategyRecommendation, StrategyConfidence

        return StrategyRecommendation(
            symbol=strategy.symbol,
            primary_strategy=strategy.recommended_strategy,
            confidence=strategy.confidence,
            confidence_level=StrategyConfidence.HIGH if strategy.confidence >= 0.8 else StrategyConfidence.MEDIUM,
            market_environment_score=0.8,
            stock_specific_score=0.85,
            erica_criteria_score=0.9,
            risk_adjusted_score=0.75,
            key_supporting_factors=strategy.entry_criteria,
            key_risk_factors=strategy.key_risks,
            alternative_strategies=[],
            recommended_dte=(30, 45),
            recommended_delta=(0.25, 0.35),
            position_size_multiplier=1.0,
            invalidation_triggers=strategy.invalidation_signals,
            upgrade_triggers=strategy.upgrade_opportunities
        )

    def toggle_auto_refresh(self):
        """Toggle auto-refresh functionality"""
        if self.auto_refresh_var.get():
            self.start_auto_refresh()
        else:
            self.stop_auto_refresh()

    def start_auto_refresh(self):
        """Start auto-refresh timer"""
        self.auto_refresh_active = True
        self._schedule_refresh()

    def stop_auto_refresh(self):
        """Stop auto-refresh timer"""
        self.auto_refresh_active = False

    def _schedule_refresh(self):
        """Schedule next auto-refresh"""
        if self.auto_refresh_active:
            self.after(self.refresh_interval * 1000, self._auto_refresh_callback)

    def _auto_refresh_callback(self):
        """Auto-refresh callback"""
        if self.auto_refresh_active:
            self.refresh_all_strategies()
            self._schedule_refresh()

    def export_dashboard_report(self):
        """Export comprehensive dashboard report"""
        from tkinter import filedialog

        filename = filedialog.asksaveasfilename(
            defaultextension=".txt",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")],
            title="Export Best Strategy Dashboard Report"
        )

        if filename:
            try:
                with open(filename, 'w') as f:
                    f.write("BEST STRATEGY DASHBOARD REPORT\n")
                    f.write("=" * 50 + "\n")
                    f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

                    for symbol, recommendation in self.current_recommendations.items():
                        f.write(f"\n{symbol} - BEST STRATEGY ANALYSIS\n")
                        f.write("-" * 30 + "\n")

                        strategy_name = recommendation.strategy.recommended_strategy.value.replace('_', ' ').title()
                        f.write(f"Recommended Strategy: {strategy_name}\n")
                        f.write(f"Confidence: {recommendation.strategy.confidence:.0%}\n")
                        f.write(f"Overall Score: {recommendation.criteria_analysis.overall_score:.0%}\n")
                        f.write(f"Recommendation Strength: {recommendation.criteria_analysis.recommendation_strength}\n\n")

                        f.write("Execution Details:\n")
                        for key, value in recommendation.execution_details.items():
                            f.write(f"  {key.replace('_', ' ').title()}: {value}\n")

                        f.write("\nFinancial Analysis:\n")
                        f.write(f"  {recommendation.financial_analysis.fundamental_summary}\n")

                        f.write(f"\nNews Sentiment: {recommendation.news_analysis.sentiment_summary}\n")

                        f.write("\n" + "="*50 + "\n")

                tk.messagebox.showinfo("Export Complete", f"Dashboard report exported to {filename}")

            except Exception as e:
                tk.messagebox.showerror("Export Error", f"Failed to export report: {e}")


def create_best_strategy_dashboard(parent, api_key: str, symbols: List[str] = None) -> BestStrategyDashboard:
    """Factory function to create best strategy dashboard"""
    return BestStrategyDashboard(parent, api_key, symbols)
