"""
Comprehensive Options Analysis Engine
Advanced options trading analysis for the AI assistant

This module provides expert-level options analysis including:
- Cash Secured Put position analysis
- News catalyst impact assessment
- Technical analysis integration
- Greeks analysis and risk assessment
- Strategic recommendations with specific action items
"""

from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import logging
import math

# Import existing system components
from daily_outline import fmp_quote, fmp_historical_daily, fmp_options_chain, fmp_earnings_next, fmp_news_today
from market_analysis_engine import MarketAnalysisEngine, MarketFactors, StockSpecificFactors
from erica_strategy_engine import EricaStrategyEngine, EricaSignals, EricaTradeSetup, EricaStrategy
from erica_decision_framework import EricaDecisionFramework

class PositionStatus(Enum):
    PROFITABLE = "profitable"
    BREAKEVEN = "breakeven"
    LOSING = "losing"
    AT_RISK = "at_risk"

class CatalystImpact(Enum):
    HIGHLY_BULLISH = "highly_bullish"
    BULLISH = "bullish"
    NEUTRAL = "neutral"
    BEARISH = "bearish"
    HIGHLY_BEARISH = "highly_bearish"

@dataclass
class CSPPosition:
    """Comprehensive Cash Secured Put position data"""
    symbol: str
    strike_price: float
    expiration_date: str
    entry_date: str
    entry_premium: float
    current_premium: float
    quantity: int
    current_stock_price: float
    days_to_expiration: int
    
    # P&L Analysis
    unrealized_pnl: float
    pnl_percentage: float
    max_profit_potential: float
    max_loss_potential: float
    breakeven_price: float
    
    # Greeks
    delta: float
    theta: float
    gamma: float
    vega: float
    
    # Risk Metrics
    probability_of_profit: float
    probability_of_assignment: float
    moneyness: float  # Strike / Current Price
    
    # Trade Management
    trade_rationale: str
    management_rules: Dict[str, Any]
    erica_compliance: Dict[str, bool]

@dataclass
class NewsCatalyst:
    """News catalyst with impact analysis"""
    title: str
    summary: str
    published_date: datetime
    source: str
    impact_assessment: CatalystImpact
    price_target_change: Optional[float]
    confidence_level: float
    relevant_keywords: List[str]

@dataclass
class TechnicalLevels:
    """Key technical analysis levels"""
    current_price: float
    support_levels: List[float]
    resistance_levels: List[float]
    trend_direction: str
    volatility_regime: str
    key_moving_averages: Dict[str, float]
    volume_analysis: Dict[str, Any]

@dataclass
class OptionsMarketData:
    """Comprehensive options market data"""
    iv_rank: float
    iv_percentile: float
    iv_30_day: float
    hv_30_day: float
    iv_hv_ratio: float
    options_volume: int
    put_call_ratio: float
    max_pain: Optional[float]
    gamma_exposure: Optional[float]

@dataclass
class ComprehensiveAnalysis:
    """Complete analysis for a CSP position"""
    position: CSPPosition
    news_catalysts: List[NewsCatalyst]
    technical_analysis: TechnicalLevels
    options_data: OptionsMarketData
    market_factors: MarketFactors
    
    # Analysis Results
    position_status: PositionStatus
    risk_assessment: str
    strategic_recommendation: str
    action_items: List[str]
    probability_scenarios: Dict[str, float]
    
    # Erica's Framework Analysis
    erica_signals: EricaSignals
    erica_recommendation: str
    management_guidance: str

class ComprehensiveOptionsAnalyzer:
    """Advanced options analysis engine for AI assistant"""
    
    def __init__(self, fmp_api_key: str):
        self.fmp_api_key = fmp_api_key
        self.market_analyzer = MarketAnalysisEngine(fmp_api_key)
        self.erica_engine = EricaStrategyEngine()
        self.erica_framework = EricaDecisionFramework()
        self.logger = logging.getLogger(__name__)
    
    def analyze_csp_position(self, symbol: str, position_data: Dict[str, Any]) -> ComprehensiveAnalysis:
        """Perform comprehensive analysis of a Cash Secured Put position"""
        try:
            # Create CSP position object
            position = self._create_csp_position(symbol, position_data)
            
            # Gather market data
            news_catalysts = self._analyze_news_catalysts(symbol)
            technical_analysis = self._perform_technical_analysis(symbol)
            options_data = self._get_options_market_data(symbol)
            market_factors = self.market_analyzer.analyze_market_factors()
            
            # Perform analysis
            position_status = self._assess_position_status(position)
            risk_assessment = self._perform_risk_assessment(position, technical_analysis, options_data)
            strategic_recommendation = self._generate_strategic_recommendation(
                position, news_catalysts, technical_analysis, options_data, market_factors
            )
            
            # Generate action items
            action_items = self._generate_action_items(position, strategic_recommendation)
            
            # Calculate probability scenarios
            probability_scenarios = self._calculate_probability_scenarios(position, technical_analysis)
            
            # Erica's framework analysis
            erica_signals = self._create_erica_signals(symbol, technical_analysis, options_data)
            erica_recommendation = self._get_erica_recommendation(position, erica_signals)
            management_guidance = self._get_management_guidance(position, erica_signals)
            
            return ComprehensiveAnalysis(
                position=position,
                news_catalysts=news_catalysts,
                technical_analysis=technical_analysis,
                options_data=options_data,
                market_factors=market_factors,
                position_status=position_status,
                risk_assessment=risk_assessment,
                strategic_recommendation=strategic_recommendation,
                action_items=action_items,
                probability_scenarios=probability_scenarios,
                erica_signals=erica_signals,
                erica_recommendation=erica_recommendation,
                management_guidance=management_guidance
            )
            
        except Exception as e:
            self.logger.error(f"Error analyzing CSP position for {symbol}: {e}")
            raise
    
    def _create_csp_position(self, symbol: str, position_data: Dict[str, Any]) -> CSPPosition:
        """Create CSP position object from data"""
        # Get current market data
        quote = fmp_quote(symbol, self.fmp_api_key)
        current_price = quote.get('price', 0) if quote else 0
        
        # Calculate derived metrics
        strike = position_data.get('strike_price', 0)
        entry_premium = position_data.get('entry_premium', 0)
        current_premium = position_data.get('current_premium', 0)
        quantity = position_data.get('quantity', 1)
        
        unrealized_pnl = (entry_premium - current_premium) * 100 * quantity
        pnl_percentage = ((entry_premium - current_premium) / entry_premium * 100) if entry_premium > 0 else 0
        breakeven_price = strike - entry_premium
        moneyness = strike / current_price if current_price > 0 else 0
        
        return CSPPosition(
            symbol=symbol,
            strike_price=strike,
            expiration_date=position_data.get('expiration_date', ''),
            entry_date=position_data.get('entry_date', ''),
            entry_premium=entry_premium,
            current_premium=current_premium,
            quantity=quantity,
            current_stock_price=current_price,
            days_to_expiration=position_data.get('days_to_expiration', 0),
            unrealized_pnl=unrealized_pnl,
            pnl_percentage=pnl_percentage,
            max_profit_potential=entry_premium * 100 * quantity,
            max_loss_potential=(strike - entry_premium) * 100 * quantity,
            breakeven_price=breakeven_price,
            delta=position_data.get('delta', -0.25),
            theta=position_data.get('theta', 0.05),
            gamma=position_data.get('gamma', 0.01),
            vega=position_data.get('vega', 0.10),
            probability_of_profit=self._calculate_pop(strike, current_price, position_data.get('days_to_expiration', 30)),
            probability_of_assignment=self._calculate_assignment_probability(moneyness, position_data.get('days_to_expiration', 30)),
            moneyness=moneyness,
            trade_rationale=position_data.get('trade_rationale', ''),
            management_rules=position_data.get('management_rules', {}),
            erica_compliance=self._check_erica_compliance(position_data)
        )
    
    def _calculate_pop(self, strike: float, current_price: float, dte: int) -> float:
        """Calculate probability of profit for CSP"""
        if current_price <= 0 or strike <= 0:
            return 0.5
        
        # Simplified calculation - in practice would use more sophisticated models
        distance_otm = (current_price - strike) / current_price
        time_factor = max(0.1, dte / 30)  # Normalize to 30 days
        
        # Higher distance OTM and more time = higher POP
        pop = 0.5 + (distance_otm * 2) + (time_factor * 0.1)
        return min(0.95, max(0.05, pop))
    
    def _calculate_assignment_probability(self, moneyness: float, dte: int) -> float:
        """Calculate probability of assignment"""
        if moneyness >= 1.0:  # ITM
            return min(0.95, 0.7 + (1.0 - moneyness) * 2)
        else:  # OTM
            time_factor = max(0.1, dte / 30)
            return max(0.05, (1.0 - moneyness) * 0.5 * time_factor)
    
    def _check_erica_compliance(self, position_data: Dict[str, Any]) -> Dict[str, bool]:
        """Check compliance with Erica's CSP rules"""
        return {
            'delta_range': -0.30 <= position_data.get('delta', 0) <= -0.15,
            'dte_range': 21 <= position_data.get('days_to_expiration', 0) <= 45,
            'profit_target': position_data.get('management_rules', {}).get('profit_target', 0) >= 0.50,
            'proper_strike_selection': True,  # Would check against support levels
            'position_sizing': True  # Would check against account size
        }

    def _analyze_news_catalysts(self, symbol: str) -> List[NewsCatalyst]:
        """Analyze news catalysts and their impact"""
        catalysts = []

        try:
            # Get recent news
            news_data = fmp_news_today([symbol], self.fmp_api_key, limit=20)

            for article in news_data:
                # Analyze impact based on keywords and content
                impact = self._assess_news_impact(article)

                catalyst = NewsCatalyst(
                    title=article.get('title', ''),
                    summary=article.get('text', '')[:200] + '...',
                    published_date=datetime.fromisoformat(article.get('publishedDate', datetime.now().isoformat())),
                    source=article.get('site', ''),
                    impact_assessment=impact,
                    price_target_change=None,  # Would extract from analyst reports
                    confidence_level=self._calculate_confidence(article),
                    relevant_keywords=self._extract_keywords(article)
                )
                catalysts.append(catalyst)

        except Exception as e:
            self.logger.error(f"Error analyzing news catalysts for {symbol}: {e}")

        return catalysts[:10]  # Return top 10 most relevant

    def _assess_news_impact(self, article: Dict[str, Any]) -> CatalystImpact:
        """Assess the impact of a news article"""
        title = article.get('title', '').lower()
        text = article.get('text', '').lower()

        # Bullish keywords
        bullish_keywords = ['upgrade', 'beat', 'strong', 'growth', 'positive', 'buy', 'outperform']
        bearish_keywords = ['downgrade', 'miss', 'weak', 'decline', 'negative', 'sell', 'underperform']

        bullish_score = sum(1 for word in bullish_keywords if word in title or word in text)
        bearish_score = sum(1 for word in bearish_keywords if word in title or word in text)

        if bullish_score > bearish_score + 1:
            return CatalystImpact.BULLISH if bullish_score <= 3 else CatalystImpact.HIGHLY_BULLISH
        elif bearish_score > bullish_score + 1:
            return CatalystImpact.BEARISH if bearish_score <= 3 else CatalystImpact.HIGHLY_BEARISH
        else:
            return CatalystImpact.NEUTRAL

    def _calculate_confidence(self, article: Dict[str, Any]) -> float:
        """Calculate confidence level for news impact"""
        # Base confidence on source reliability and content quality
        source = article.get('site', '').lower()

        high_quality_sources = ['reuters', 'bloomberg', 'wsj', 'cnbc', 'marketwatch']
        if any(source_name in source for source_name in high_quality_sources):
            return 0.8
        else:
            return 0.6

    def _extract_keywords(self, article: Dict[str, Any]) -> List[str]:
        """Extract relevant keywords from article"""
        title = article.get('title', '').lower()

        # Key trading-related terms
        keywords = []
        trading_terms = ['earnings', 'revenue', 'guidance', 'upgrade', 'downgrade', 'target',
                        'analyst', 'rating', 'buy', 'sell', 'hold', 'merger', 'acquisition']

        for term in trading_terms:
            if term in title:
                keywords.append(term)

        return keywords

    def _perform_technical_analysis(self, symbol: str) -> TechnicalLevels:
        """Perform comprehensive technical analysis"""
        try:
            # Get historical data
            historical_data = fmp_historical_daily(symbol, self.fmp_api_key, days=252)
            if not historical_data:
                raise ValueError("No historical data available")

            # Extract price data
            prices = [float(day['close']) for day in historical_data[-100:]]
            volumes = [float(day['volume']) for day in historical_data[-100:]]
            current_price = prices[-1]

            # Calculate support and resistance levels
            support_levels = self._calculate_support_levels(prices)
            resistance_levels = self._calculate_resistance_levels(prices)

            # Determine trend
            trend_direction = self._determine_trend(prices)

            # Assess volatility regime
            volatility_regime = self._assess_volatility_regime(prices)

            # Calculate moving averages
            ma_20 = sum(prices[-20:]) / 20
            ma_50 = sum(prices[-50:]) / 50 if len(prices) >= 50 else ma_20
            ma_200 = sum(prices[-200:]) / 200 if len(prices) >= 200 else ma_50

            # Volume analysis
            avg_volume = sum(volumes[-20:]) / 20
            recent_volume = volumes[-1]

            return TechnicalLevels(
                current_price=current_price,
                support_levels=support_levels,
                resistance_levels=resistance_levels,
                trend_direction=trend_direction,
                volatility_regime=volatility_regime,
                key_moving_averages={
                    'ma_20': ma_20,
                    'ma_50': ma_50,
                    'ma_200': ma_200
                },
                volume_analysis={
                    'average_volume': avg_volume,
                    'recent_volume': recent_volume,
                    'volume_ratio': recent_volume / avg_volume if avg_volume > 0 else 1.0
                }
            )

        except Exception as e:
            self.logger.error(f"Error performing technical analysis for {symbol}: {e}")
            # Return default values
            return TechnicalLevels(
                current_price=0,
                support_levels=[],
                resistance_levels=[],
                trend_direction='neutral',
                volatility_regime='normal',
                key_moving_averages={},
                volume_analysis={}
            )

    def _calculate_support_levels(self, prices: List[float]) -> List[float]:
        """Calculate key support levels"""
        if len(prices) < 20:
            return []

        # Find local minima as support levels
        support_levels = []
        for i in range(10, len(prices) - 10):
            if prices[i] == min(prices[i-10:i+10]):
                support_levels.append(prices[i])

        # Remove duplicates and sort
        support_levels = sorted(list(set(support_levels)))
        return support_levels[-3:]  # Return top 3 most recent

    def _calculate_resistance_levels(self, prices: List[float]) -> List[float]:
        """Calculate key resistance levels"""
        if len(prices) < 20:
            return []

        # Find local maxima as resistance levels
        resistance_levels = []
        for i in range(10, len(prices) - 10):
            if prices[i] == max(prices[i-10:i+10]):
                resistance_levels.append(prices[i])

        # Remove duplicates and sort
        resistance_levels = sorted(list(set(resistance_levels)), reverse=True)
        return resistance_levels[:3]  # Return top 3 highest

    def _determine_trend(self, prices: List[float]) -> str:
        """Determine overall trend direction"""
        if len(prices) < 20:
            return 'neutral'

        # Compare recent prices to older prices
        recent_avg = sum(prices[-10:]) / 10
        older_avg = sum(prices[-30:-20]) / 10

        change_pct = (recent_avg - older_avg) / older_avg

        if change_pct > 0.05:
            return 'bullish'
        elif change_pct < -0.05:
            return 'bearish'
        else:
            return 'neutral'

    def _assess_volatility_regime(self, prices: List[float]) -> str:
        """Assess current volatility regime"""
        if len(prices) < 30:
            return 'normal'

        # Calculate recent volatility
        returns = [(prices[i] - prices[i-1]) / prices[i-1] for i in range(1, len(prices))]
        recent_vol = (sum(r**2 for r in returns[-20:]) / 20) ** 0.5
        historical_vol = (sum(r**2 for r in returns) / len(returns)) ** 0.5

        vol_ratio = recent_vol / historical_vol if historical_vol > 0 else 1.0

        if vol_ratio > 1.5:
            return 'high'
        elif vol_ratio < 0.7:
            return 'low'
        else:
            return 'normal'

    def _get_options_market_data(self, symbol: str) -> OptionsMarketData:
        """Get comprehensive options market data"""
        try:
            # Get options chain
            options_chain = fmp_options_chain(symbol, self.fmp_api_key)

            if not options_chain:
                return OptionsMarketData(
                    iv_rank=0.5, iv_percentile=0.5, iv_30_day=0.3, hv_30_day=0.25,
                    iv_hv_ratio=1.2, options_volume=1000, put_call_ratio=1.0,
                    max_pain=None, gamma_exposure=None
                )

            # Calculate options metrics
            total_volume = sum(opt.get('volume', 0) for opt in options_chain)
            put_volume = sum(opt.get('volume', 0) for opt in options_chain if opt.get('type') == 'put')
            call_volume = total_volume - put_volume
            put_call_ratio = put_volume / call_volume if call_volume > 0 else 1.0

            # Calculate average IV
            iv_values = [opt.get('impliedVolatility', 0) for opt in options_chain if opt.get('impliedVolatility')]
            avg_iv = sum(iv_values) / len(iv_values) if iv_values else 0.3

            return OptionsMarketData(
                iv_rank=0.6,  # Would calculate from historical IV data
                iv_percentile=0.65,  # Would calculate from historical IV data
                iv_30_day=avg_iv,
                hv_30_day=0.25,  # Would calculate from price history
                iv_hv_ratio=avg_iv / 0.25 if avg_iv > 0 else 1.0,
                options_volume=total_volume,
                put_call_ratio=put_call_ratio,
                max_pain=None,  # Would calculate from options chain
                gamma_exposure=None  # Would calculate from options chain
            )

        except Exception as e:
            self.logger.error(f"Error getting options market data for {symbol}: {e}")
            return OptionsMarketData(
                iv_rank=0.5, iv_percentile=0.5, iv_30_day=0.3, hv_30_day=0.25,
                iv_hv_ratio=1.2, options_volume=1000, put_call_ratio=1.0,
                max_pain=None, gamma_exposure=None
            )

    def _assess_position_status(self, position: CSPPosition) -> PositionStatus:
        """Assess current position status"""
        if position.pnl_percentage > 10:
            return PositionStatus.PROFITABLE
        elif -5 <= position.pnl_percentage <= 10:
            return PositionStatus.BREAKEVEN
        elif position.pnl_percentage < -20:
            return PositionStatus.AT_RISK
        else:
            return PositionStatus.LOSING

    def _perform_risk_assessment(self, position: CSPPosition, technical: TechnicalLevels,
                                options: OptionsMarketData) -> str:
        """Perform comprehensive risk assessment"""
        risk_factors = []

        # Assignment risk
        if position.moneyness > 0.95:
            risk_factors.append("HIGH assignment risk - position is near/in the money")
        elif position.moneyness > 0.90:
            risk_factors.append("MODERATE assignment risk - position approaching the money")

        # Time decay risk
        if position.days_to_expiration < 10:
            risk_factors.append("HIGH time decay acceleration - position near expiration")

        # Technical risk
        if technical.support_levels:
            nearest_support = min(technical.support_levels, key=lambda x: abs(x - position.strike_price))
            if position.strike_price < nearest_support * 0.98:
                risk_factors.append("MODERATE technical risk - strike below key support")

        # Volatility risk
        if options.iv_hv_ratio > 1.5:
            risk_factors.append("MODERATE volatility risk - IV elevated vs historical")
        elif options.iv_hv_ratio < 0.8:
            risk_factors.append("LOW volatility risk - IV compressed")

        if not risk_factors:
            return "LOW overall risk - position well-positioned with manageable exposure"
        else:
            return "Risk factors identified: " + "; ".join(risk_factors)

    def _generate_strategic_recommendation(self, position: CSPPosition, catalysts: List[NewsCatalyst],
                                         technical: TechnicalLevels, options: OptionsMarketData,
                                         market_factors: MarketFactors) -> str:
        """Generate strategic recommendation"""

        # Analyze current situation
        bullish_catalysts = sum(1 for c in catalysts if c.impact_assessment in [CatalystImpact.BULLISH, CatalystImpact.HIGHLY_BULLISH])
        bearish_catalysts = sum(1 for c in catalysts if c.impact_assessment in [CatalystImpact.BEARISH, CatalystImpact.HIGHLY_BEARISH])

        # Check profit target achievement
        if position.pnl_percentage >= 50:
            return "CLOSE POSITION - Profit target achieved (50%+ of max profit). Take profits and look for new opportunities."

        # Check assignment risk
        if position.moneyness > 0.98 and position.days_to_expiration < 7:
            return "CONSIDER ROLLING - High assignment risk. Roll to next expiration cycle if still bullish on underlying."

        # Check time decay acceleration
        if position.days_to_expiration < 5 and position.pnl_percentage < 25:
            return "EVALUATE EXIT - Approaching expiration with limited profit. Consider closing to avoid assignment."

        # Market sentiment analysis
        if bearish_catalysts > bullish_catalysts and position.moneyness > 0.90:
            return "DEFENSIVE ACTION - Bearish catalysts present with position at risk. Consider rolling down and out."

        if bullish_catalysts > bearish_catalysts and position.pnl_percentage > 25:
            return "HOLD POSITION - Bullish catalysts support holding. Monitor for profit-taking opportunity."

        return "MONITOR POSITION - Continue holding with current management rules. Watch for profit target or risk triggers."

    def _generate_action_items(self, position: CSPPosition, recommendation: str) -> List[str]:
        """Generate specific action items"""
        actions = []

        if "CLOSE" in recommendation:
            actions.append(f"Place order to buy back {position.symbol} {position.expiration_date} ${position.strike_price} PUT")
            actions.append("Calculate final P&L and update trading journal")
            actions.append("Screen for new CSP opportunities")

        elif "ROLL" in recommendation:
            actions.append(f"Research next expiration cycle for {position.symbol}")
            actions.append("Calculate net credit/debit for rolling transaction")
            actions.append("Set alerts for optimal rolling timing")

        elif "MONITOR" in recommendation:
            actions.append(f"Set price alert at ${position.strike_price * 1.02:.2f} (2% above strike)")
            actions.append("Monitor daily P&L and theta decay")
            actions.append("Review position at 21 DTE for management decision")

        # Always include risk management
        actions.append("Maintain position size within risk parameters")
        actions.append("Update stop-loss levels if applicable")

        return actions

    def _calculate_probability_scenarios(self, position: CSPPosition, technical: TechnicalLevels) -> Dict[str, float]:
        """Calculate probability scenarios for position outcomes"""
        current_price = technical.current_price
        strike = position.strike_price
        dte = position.days_to_expiration

        # Simplified probability calculations
        scenarios = {}

        # Probability of expiring worthless (max profit)
        distance_otm = (current_price - strike) / current_price if current_price > 0 else 0
        scenarios['expires_worthless'] = min(0.9, 0.5 + distance_otm * 2)

        # Probability of assignment
        scenarios['assignment'] = 1.0 - scenarios['expires_worthless']

        # Probability of achieving 50% profit target
        if position.pnl_percentage >= 50:
            scenarios['profit_target_achieved'] = 1.0
        else:
            scenarios['profit_target_achieved'] = min(0.8, scenarios['expires_worthless'] * 0.8)

        # Probability of loss (assignment below breakeven)
        scenarios['loss_scenario'] = max(0.05, scenarios['assignment'] * 0.3)

        return scenarios

    def _create_erica_signals(self, symbol: str, technical: TechnicalLevels, options: OptionsMarketData) -> EricaSignals:
        """Create Erica signals for framework analysis"""
        return EricaSignals(
            spot_price=technical.current_price,
            earnings_days_away=30,  # Would get from earnings calendar
            iv_rank=options.iv_rank,
            iv_percentile=options.iv_percentile,
            expected_move=None,  # Would calculate from options data
            atr=technical.current_price * 0.02,  # Simplified ATR
            support_level=technical.support_levels[0] if technical.support_levels else None,
            resistance_level=technical.resistance_levels[0] if technical.resistance_levels else None
        )

    def _get_erica_recommendation(self, position: CSPPosition, signals: EricaSignals) -> str:
        """Get recommendation based on Erica's framework"""
        # Use Erica's decision framework
        try:
            suitability_score, reasoning = self.erica_framework.evaluate_strategy_suitability(
                EricaStrategy.CASH_SECURED_PUT,
                None,  # Would pass market factors
                None,  # Would pass stock factors
                position.symbol
            )

            if suitability_score > 0.7:
                return "STRONG alignment with Erica's CSP criteria. Position well-structured."
            elif suitability_score > 0.5:
                return "MODERATE alignment with Erica's criteria. Monitor for improvements."
            else:
                return "WEAK alignment with Erica's criteria. Consider adjustments."

        except Exception as e:
            return "Unable to assess Erica framework alignment."

    def _get_management_guidance(self, position: CSPPosition, signals: EricaSignals) -> str:
        """Get management guidance based on Erica's rules"""
        guidance = []

        # Profit target guidance
        if position.pnl_percentage >= 50:
            guidance.append("✓ Profit target achieved - consider closing per Erica's 50% rule")

        # Time management
        if position.days_to_expiration <= 21:
            guidance.append("⚠ Approaching 21 DTE management threshold")

        # Delta management
        if abs(position.delta) > 0.35:
            guidance.append("⚠ Delta breach - consider rolling per Erica's delta management")

        # Assignment preparation
        if position.moneyness > 0.95:
            guidance.append("⚠ Prepare for potential assignment - ensure adequate buying power")

        if not guidance:
            guidance.append("✓ Position within normal management parameters")

        return " | ".join(guidance)
