"""
Enhanced Strategy Recommendation Engine for Erica's Trading System

Integrates real-time market analysis with comprehensive strategy rationale,
confidence scoring, and market condition awareness.
"""

from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from datetime import datetime
import logging

from strategy_knowledge_base import StrategyKnowledgeBase, StrategyProfile
from market_intelligence import MarketIntelligenceEngine, MarketIntelligence
from intelligent_strategy_engine import IntelligentStrategyEngine, StrategyOfTheDay
from market_analysis_engine import StockSpecificFactors
from daily_outline import StrategyType

@dataclass
class EnhancedStrategyRecommendation:
    """Enhanced strategy recommendation with comprehensive analysis"""
    symbol: str
    primary_strategy: StrategyType
    confidence: float
    
    # Enhanced analysis
    strategy_rationale: str
    market_context: str
    risk_assessment: str
    position_sizing_guidance: str
    
    # Execution details
    entry_criteria: List[str]
    exit_criteria: List[str]
    risk_management_rules: List[str]
    
    # Alternative strategies
    alternative_strategies: List[Tuple[StrategyType, float, str]]
    
    # Market factors influence
    iv_rank_impact: str
    earnings_impact: str
    technical_impact: str
    sector_impact: str
    
    # Educational content
    strategy_explanation: str
    why_this_strategy: str
    common_mistakes: List[str]
    success_tips: List[str]

class EnhancedStrategyEngine:
    """Enhanced strategy recommendation engine with comprehensive analysis"""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_engine = IntelligentStrategyEngine(api_key)
        self.intelligence_engine = MarketIntelligenceEngine(api_key)
        self.strategy_kb = StrategyKnowledgeBase()
        self.logger = logging.getLogger(__name__)
    
    def get_enhanced_recommendation(self, symbol: str) -> EnhancedStrategyRecommendation:
        """Get comprehensive enhanced strategy recommendation for a symbol"""
        
        try:
            # Get base recommendation
            market_report, strategies = self.base_engine.generate_daily_recommendations([symbol])
            base_strategy = strategies[0] if strategies else None
            
            if not base_strategy:
                return self._get_fallback_recommendation(symbol)
            
            # Get market intelligence
            intelligence = self.intelligence_engine.get_market_intelligence()
            
            # Get stock factors
            stock_factors = self.base_engine.market_analyzer.analyze_stock_factors(symbol)
            
            # Get strategy profile
            strategy_name = self._map_strategy_type_to_name(base_strategy.recommended_strategy)
            strategy_profile = self.strategy_kb.strategies.get(strategy_name)
            
            # Build enhanced recommendation
            enhanced = self._build_enhanced_recommendation(
                base_strategy, intelligence, stock_factors, strategy_profile
            )
            
            return enhanced
            
        except Exception as e:
            self.logger.error(f"Error generating enhanced recommendation for {symbol}: {e}")
            return self._get_fallback_recommendation(symbol)
    
    def get_market_strategy_overview(self, symbols: List[str]) -> Dict[str, Any]:
        """Get comprehensive market strategy overview for multiple symbols"""
        
        try:
            # Get market intelligence
            intelligence = self.intelligence_engine.get_market_intelligence()
            
            # Get recommendations for all symbols
            recommendations = {}
            for symbol in symbols:
                recommendations[symbol] = self.get_enhanced_recommendation(symbol)
            
            # Analyze overall strategy distribution
            strategy_distribution = self._analyze_strategy_distribution(recommendations)
            
            # Generate market-wide insights
            market_insights = self._generate_market_insights(intelligence, recommendations)
            
            return {
                'market_intelligence': intelligence,
                'symbol_recommendations': recommendations,
                'strategy_distribution': strategy_distribution,
                'market_insights': market_insights,
                'action_plan': self._generate_action_plan(intelligence, recommendations)
            }
            
        except Exception as e:
            self.logger.error(f"Error generating market strategy overview: {e}")
            return self._get_fallback_overview(symbols)
    
    def _build_enhanced_recommendation(self, base_strategy: StrategyOfTheDay, 
                                     intelligence: MarketIntelligence,
                                     stock_factors: StockSpecificFactors,
                                     strategy_profile: Optional[StrategyProfile]) -> EnhancedStrategyRecommendation:
        """Build comprehensive enhanced recommendation"""
        
        # Strategy rationale
        rationale = self._generate_strategy_rationale(base_strategy, intelligence, stock_factors)
        
        # Market context
        market_context = self._generate_market_context(intelligence, stock_factors)
        
        # Risk assessment
        risk_assessment = self._generate_risk_assessment(intelligence, stock_factors, strategy_profile)
        
        # Position sizing guidance
        position_sizing = self._generate_position_sizing_guidance(intelligence, stock_factors)
        
        # Entry/exit criteria
        entry_criteria = self._generate_entry_criteria(base_strategy, stock_factors, strategy_profile)
        exit_criteria = self._generate_exit_criteria(base_strategy, stock_factors, strategy_profile)
        risk_rules = self._generate_risk_management_rules(strategy_profile)
        
        # Alternative strategies
        alternatives = self._generate_alternative_strategies(base_strategy, intelligence, stock_factors)
        
        # Factor impacts
        iv_impact = self._analyze_iv_impact(stock_factors, strategy_profile)
        earnings_impact = self._analyze_earnings_impact(stock_factors, strategy_profile)
        technical_impact = self._analyze_technical_impact(stock_factors, strategy_profile)
        sector_impact = self._analyze_sector_impact(intelligence, stock_factors)
        
        # Educational content
        strategy_explanation = strategy_profile.how_it_works if strategy_profile else "Strategy explanation not available"
        why_this_strategy = self._explain_strategy_selection(base_strategy, intelligence, stock_factors)
        common_mistakes = strategy_profile.common_mistakes if strategy_profile else []
        success_tips = strategy_profile.success_tips if strategy_profile else []
        
        return EnhancedStrategyRecommendation(
            symbol=base_strategy.symbol,
            primary_strategy=base_strategy.recommended_strategy,
            confidence=base_strategy.confidence,
            strategy_rationale=rationale,
            market_context=market_context,
            risk_assessment=risk_assessment,
            position_sizing_guidance=position_sizing,
            entry_criteria=entry_criteria,
            exit_criteria=exit_criteria,
            risk_management_rules=risk_rules,
            alternative_strategies=alternatives,
            iv_rank_impact=iv_impact,
            earnings_impact=earnings_impact,
            technical_impact=technical_impact,
            sector_impact=sector_impact,
            strategy_explanation=strategy_explanation,
            why_this_strategy=why_this_strategy,
            common_mistakes=common_mistakes,
            success_tips=success_tips
        )
    
    def _generate_strategy_rationale(self, strategy: StrategyOfTheDay, 
                                   intelligence: MarketIntelligence,
                                   stock_factors: StockSpecificFactors) -> str:
        """Generate detailed strategy rationale"""
        
        rationale_parts = []
        
        # IV rank consideration
        if stock_factors.iv_rank > 0.6:
            rationale_parts.append(f"High IV rank ({stock_factors.iv_rank:.0%}) favors premium selling strategies")
        elif stock_factors.iv_rank < 0.4:
            rationale_parts.append(f"Low IV rank ({stock_factors.iv_rank:.0%}) favors premium buying strategies")
        
        # Earnings consideration
        if stock_factors.earnings_days_away and stock_factors.earnings_days_away <= 10:
            rationale_parts.append(f"Earnings in {stock_factors.earnings_days_away} days requires conservative positioning")
        
        # Technical consideration
        if stock_factors.trend_alignment == 'up':
            rationale_parts.append("Upward trend supports bullish strategies")
        elif stock_factors.trend_alignment == 'down':
            rationale_parts.append("Downward trend suggests defensive positioning")
        
        # Market regime consideration
        if intelligence.volatility_regime.value in ['elevated', 'high']:
            rationale_parts.append("Elevated volatility environment supports premium selling")
        
        return "; ".join(rationale_parts) if rationale_parts else "Balanced market conditions support this strategy"
    
    def _generate_market_context(self, intelligence: MarketIntelligence, 
                                stock_factors: StockSpecificFactors) -> str:
        """Generate market context analysis"""
        
        context = f"Market Environment: {intelligence.trend_direction.value.replace('_', ' ').title()} trend "
        context += f"in {intelligence.volatility_regime.value.replace('_', ' ')} volatility regime. "
        context += f"VIX at {intelligence.vix_level:.1f} ({intelligence.vix_percentile:.0f}th percentile). "
        
        if intelligence.leading_sectors:
            context += f"Leading sectors: {', '.join(intelligence.leading_sectors)}. "
        
        context += f"Stock-specific: {stock_factors.trend_alignment} trend, "
        context += f"IV rank {stock_factors.iv_rank:.0%}."
        
        return context
    
    def _generate_risk_assessment(self, intelligence: MarketIntelligence,
                                 stock_factors: StockSpecificFactors,
                                 strategy_profile: Optional[StrategyProfile]) -> str:
        """Generate comprehensive risk assessment"""
        
        risk_factors = []
        
        # Market risk
        if intelligence.risk_level == "High":
            risk_factors.append("High market risk environment")
        
        # Volatility risk
        if intelligence.vix_level > 30:
            risk_factors.append("Extreme volatility increases all position risks")
        
        # Earnings risk
        if stock_factors.earnings_days_away and stock_factors.earnings_days_away <= 5:
            risk_factors.append("Imminent earnings create significant event risk")
        
        # Strategy-specific risks
        if strategy_profile and strategy_profile.name == "Credit Spread":
            risk_factors.append("Limited profit potential with defined maximum loss")
        elif strategy_profile and strategy_profile.name == "Covered Call":
            risk_factors.append("Unlimited downside risk on underlying shares")
        
        if not risk_factors:
            return "Low to moderate risk environment with standard strategy risks"
        
        return "Risk factors: " + "; ".join(risk_factors)
    
    def _generate_position_sizing_guidance(self, intelligence: MarketIntelligence,
                                         stock_factors: StockSpecificFactors) -> str:
        """Generate position sizing guidance"""
        
        base_risk = 0.02  # 2% base risk
        
        # Adjust for market conditions
        if intelligence.risk_level == "High":
            adjusted_risk = base_risk * 0.5
            guidance = f"Reduce position size to {adjusted_risk:.1%} of account due to high market risk"
        elif intelligence.risk_level == "Low":
            adjusted_risk = base_risk * 1.5
            guidance = f"Can increase position size to {adjusted_risk:.1%} of account in low risk environment"
        else:
            adjusted_risk = base_risk
            guidance = f"Standard position sizing: {adjusted_risk:.1%} of account per trade"
        
        # Earnings adjustment
        if stock_factors.earnings_days_away and stock_factors.earnings_days_away <= 7:
            guidance += ". Reduce further due to earnings proximity"
        
        return guidance
    
    def _generate_entry_criteria(self, strategy: StrategyOfTheDay,
                               stock_factors: StockSpecificFactors,
                               strategy_profile: Optional[StrategyProfile]) -> List[str]:
        """Generate entry criteria for the strategy"""
        
        criteria = []
        
        # General criteria
        criteria.append("Confirm market direction aligns with strategy bias")
        criteria.append("Verify adequate liquidity in options chain")
        
        # Strategy-specific criteria
        if strategy.recommended_strategy == StrategyType.CREDIT_SPREAD:
            criteria.append("IV rank > 50th percentile preferred")
            criteria.append("Use technical levels for strike selection")
            criteria.append("Target 30-45 DTE for optimal time decay")
        
        elif strategy.recommended_strategy == StrategyType.COVERED_CALL:
            criteria.append("Own 100 shares or willing to purchase")
            criteria.append("Select strike above resistance if available")
            criteria.append("Target 0.20-0.30 delta for income generation")
        
        elif strategy.recommended_strategy == StrategyType.LEAPS:
            criteria.append("IV rank < 50th percentile preferred")
            criteria.append("Strong fundamental outlook required")
            criteria.append("Select ITM strikes for stability")
        
        # Earnings considerations
        if stock_factors.earnings_days_away and stock_factors.earnings_days_away <= 10:
            criteria.append("Use conservative strikes due to earnings proximity")
        
        return criteria
    
    def _generate_exit_criteria(self, strategy: StrategyOfTheDay,
                              stock_factors: StockSpecificFactors,
                              strategy_profile: Optional[StrategyProfile]) -> List[str]:
        """Generate exit criteria for the strategy"""
        
        criteria = []
        
        # Profit targets
        if strategy.recommended_strategy in [StrategyType.CREDIT_SPREAD, StrategyType.PREMIUM_SELLING]:
            criteria.append("Take profits at 50-70% of maximum gain")
        else:
            criteria.append("Take profits at 30-50% of maximum gain")
        
        # Time-based exits
        criteria.append("Close or roll positions at 21 DTE")
        
        # Risk management exits
        criteria.append("Exit if thesis changes or technical levels break")
        
        # Strategy-specific exits
        if strategy.recommended_strategy == StrategyType.COVERED_CALL:
            criteria.append("Roll up/out if strike threatened and want to keep shares")
        
        elif strategy.recommended_strategy == StrategyType.CREDIT_SPREAD:
            criteria.append("Roll out if thesis intact but position challenged")
        
        return criteria
    
    def _generate_risk_management_rules(self, strategy_profile: Optional[StrategyProfile]) -> List[str]:
        """Generate risk management rules"""
        
        rules = [
            "Never risk more than 2% of account on single trade",
            "Maintain maximum 25% exposure to any single underlying",
            "Monitor positions daily for changes in thesis"
        ]
        
        if strategy_profile and strategy_profile.risk_management:
            risk_mgmt = strategy_profile.risk_management
            if 'stop_loss' in risk_mgmt:
                rules.append(f"Stop loss: {risk_mgmt['stop_loss']}")
            if 'profit_target' in risk_mgmt:
                rules.append(f"Profit target: {risk_mgmt['profit_target']}")
        
        return rules
    
    def _generate_alternative_strategies(self, strategy: StrategyOfTheDay,
                                       intelligence: MarketIntelligence,
                                       stock_factors: StockSpecificFactors) -> List[Tuple[StrategyType, float, str]]:
        """Generate alternative strategy recommendations"""
        
        alternatives = []
        
        # If primary is credit spread, consider covered calls
        if strategy.recommended_strategy == StrategyType.CREDIT_SPREAD:
            if stock_factors.iv_rank > 0.4:
                alternatives.append((
                    StrategyType.COVERED_CALL,
                    0.6,
                    "Alternative income strategy if you own shares"
                ))
        
        # If primary is covered call, consider credit spreads
        elif strategy.recommended_strategy == StrategyType.COVERED_CALL:
            if stock_factors.iv_rank > 0.6:
                alternatives.append((
                    StrategyType.CREDIT_SPREAD,
                    0.7,
                    "Higher probability strategy in high IV environment"
                ))
        
        # Always consider LEAPS in low IV
        if stock_factors.iv_rank < 0.4:
            alternatives.append((
                StrategyType.LEAPS,
                0.5,
                "Long-term bullish play in low IV environment"
            ))
        
        return alternatives
    
    def _analyze_iv_impact(self, stock_factors: StockSpecificFactors,
                          strategy_profile: Optional[StrategyProfile]) -> str:
        """Analyze IV rank impact on strategy"""
        
        iv_rank = stock_factors.iv_rank
        
        if iv_rank > 0.7:
            return f"Very high IV rank ({iv_rank:.0%}) strongly favors premium selling strategies"
        elif iv_rank > 0.5:
            return f"Elevated IV rank ({iv_rank:.0%}) favors premium selling strategies"
        elif iv_rank < 0.3:
            return f"Low IV rank ({iv_rank:.0%}) favors premium buying strategies"
        else:
            return f"Moderate IV rank ({iv_rank:.0%}) allows for balanced approach"
    
    def _analyze_earnings_impact(self, stock_factors: StockSpecificFactors,
                               strategy_profile: Optional[StrategyProfile]) -> str:
        """Analyze earnings proximity impact"""
        
        if not stock_factors.earnings_days_away:
            return "No immediate earnings impact"
        
        days = stock_factors.earnings_days_away
        
        if days <= 3:
            return f"Earnings in {days} days - high risk, consider avoiding new positions"
        elif days <= 7:
            return f"Earnings in {days} days - use conservative strikes and smaller size"
        elif days <= 14:
            return f"Earnings in {days} days - opportunity for earnings plays with proper risk management"
        else:
            return f"Earnings in {days} days - minimal immediate impact"
    
    def _analyze_technical_impact(self, stock_factors: StockSpecificFactors,
                                strategy_profile: Optional[StrategyProfile]) -> str:
        """Analyze technical factors impact"""
        
        impact_parts = []
        
        if stock_factors.trend_alignment == 'up':
            impact_parts.append("Upward trend supports bullish strategies")
        elif stock_factors.trend_alignment == 'down':
            impact_parts.append("Downward trend suggests defensive positioning")
        
        if stock_factors.support_level and stock_factors.resistance_level:
            impact_parts.append("Clear support/resistance levels aid in strike selection")
        
        if stock_factors.technical_confluence_score > 0.7:
            impact_parts.append("Strong technical confluence increases strategy confidence")
        
        return "; ".join(impact_parts) if impact_parts else "Mixed technical signals"
    
    def _analyze_sector_impact(self, intelligence: MarketIntelligence,
                             stock_factors: StockSpecificFactors) -> str:
        """Analyze sector rotation impact"""
        
        # This would need sector mapping for the specific stock
        # For now, provide general sector impact
        
        if intelligence.sector_rotation_signal == 'growth':
            return "Growth sector rotation supports technology and growth stocks"
        elif intelligence.sector_rotation_signal == 'value':
            return "Value rotation favors financials and industrials"
        elif intelligence.sector_rotation_signal == 'defensive':
            return "Defensive rotation supports utilities and consumer staples"
        else:
            return "Neutral sector rotation - stock-specific factors more important"
    
    def _explain_strategy_selection(self, strategy: StrategyOfTheDay,
                                  intelligence: MarketIntelligence,
                                  stock_factors: StockSpecificFactors) -> str:
        """Explain why this specific strategy was selected"""
        
        explanation = f"{strategy.recommended_strategy.value.replace('_', ' ').title()} was selected because: "
        
        reasons = []
        
        # IV-based reasoning
        if stock_factors.iv_rank > 0.6 and strategy.recommended_strategy in [StrategyType.CREDIT_SPREAD, StrategyType.PREMIUM_SELLING]:
            reasons.append(f"high IV rank ({stock_factors.iv_rank:.0%}) favors premium selling")
        
        # Trend-based reasoning
        if stock_factors.trend_alignment == 'up' and strategy.recommended_strategy == StrategyType.COVERED_CALL:
            reasons.append("upward trend supports covered call income generation")
        
        # Market regime reasoning
        if intelligence.volatility_regime.value in ['elevated', 'high']:
            reasons.append("elevated volatility environment supports this strategy")
        
        if reasons:
            explanation += ", ".join(reasons)
        else:
            explanation += "it aligns with current market conditions and stock-specific factors"
        
        return explanation
    
    def _map_strategy_type_to_name(self, strategy_type: StrategyType) -> str:
        """Map StrategyType enum to strategy knowledge base names"""
        
        mapping = {
            StrategyType.COVERED_CALL: "covered_call",
            StrategyType.CREDIT_SPREAD: "credit_spread",
            StrategyType.LEAPS: "leaps",
            StrategyType.PREMIUM_SELLING: "premium_selling"
        }
        
        return mapping.get(strategy_type, "covered_call")
    
    def _analyze_strategy_distribution(self, recommendations: Dict[str, EnhancedStrategyRecommendation]) -> Dict[str, Any]:
        """Analyze distribution of strategies across symbols"""
        
        strategy_counts = {}
        total_confidence = 0
        
        for rec in recommendations.values():
            strategy_name = rec.primary_strategy.value
            strategy_counts[strategy_name] = strategy_counts.get(strategy_name, 0) + 1
            total_confidence += rec.confidence
        
        avg_confidence = total_confidence / len(recommendations) if recommendations else 0
        
        return {
            'strategy_counts': strategy_counts,
            'average_confidence': avg_confidence,
            'total_symbols': len(recommendations)
        }
    
    def _generate_market_insights(self, intelligence: MarketIntelligence,
                                recommendations: Dict[str, EnhancedStrategyRecommendation]) -> List[str]:
        """Generate market-wide insights"""
        
        insights = []
        
        # Strategy distribution insights
        strategy_counts = {}
        for rec in recommendations.values():
            strategy_name = rec.primary_strategy.value
            strategy_counts[strategy_name] = strategy_counts.get(strategy_name, 0) + 1
        
        most_common = max(strategy_counts.items(), key=lambda x: x[1]) if strategy_counts else None
        if most_common:
            insights.append(f"Most recommended strategy: {most_common[0]} ({most_common[1]} symbols)")
        
        # Market regime insights
        insights.append(f"Market regime: {intelligence.current_regime.value.replace('_', ' ').title()}")
        insights.append(f"Volatility environment: {intelligence.volatility_regime.value.replace('_', ' ').title()}")
        
        # Risk insights
        if intelligence.risk_level == "High":
            insights.append("High risk environment - consider reduced position sizing")
        
        return insights
    
    def _generate_action_plan(self, intelligence: MarketIntelligence,
                            recommendations: Dict[str, EnhancedStrategyRecommendation]) -> List[str]:
        """Generate actionable trading plan"""
        
        plan = []
        
        # Priority trades
        high_conf_recs = [(symbol, rec) for symbol, rec in recommendations.items() if rec.confidence >= 0.7]
        if high_conf_recs:
            plan.append("Priority trades:")
            for symbol, rec in high_conf_recs[:3]:
                plan.append(f"  • {symbol}: {rec.primary_strategy.value.replace('_', ' ').title()}")
        
        # Market considerations
        plan.append(f"Market environment: {intelligence.strategy_rationale}")
        plan.append(f"Risk level: {intelligence.risk_level}")
        
        # Position sizing
        if intelligence.risk_level == "High":
            plan.append("Use reduced position sizing due to elevated risk")
        
        return plan
    
    def _get_fallback_recommendation(self, symbol: str) -> EnhancedStrategyRecommendation:
        """Provide fallback recommendation when analysis fails"""
        
        return EnhancedStrategyRecommendation(
            symbol=symbol,
            primary_strategy=StrategyType.COVERED_CALL,
            confidence=0.5,
            strategy_rationale="Fallback recommendation due to analysis error",
            market_context="Unable to analyze current market conditions",
            risk_assessment="Standard risk considerations apply",
            position_sizing_guidance="Use standard 2% position sizing",
            entry_criteria=["Verify market conditions before entry"],
            exit_criteria=["Take profits at 50% of maximum gain"],
            risk_management_rules=["Limit risk to 2% of account"],
            alternative_strategies=[],
            iv_rank_impact="Unable to determine IV impact",
            earnings_impact="Check earnings calendar before trading",
            technical_impact="Verify technical levels",
            sector_impact="Consider sector trends",
            strategy_explanation="Covered calls generate income from owned shares",
            why_this_strategy="Conservative fallback strategy",
            common_mistakes=["Not checking market conditions"],
            success_tips=["Always verify analysis before trading"]
        )
    
    def _get_fallback_overview(self, symbols: List[str]) -> Dict[str, Any]:
        """Provide fallback overview when analysis fails"""
        
        return {
            'market_intelligence': None,
            'symbol_recommendations': {symbol: self._get_fallback_recommendation(symbol) for symbol in symbols},
            'strategy_distribution': {'covered_call': len(symbols)},
            'market_insights': ["Analysis unavailable - use caution"],
            'action_plan': ["Verify market conditions before trading"]
        }
