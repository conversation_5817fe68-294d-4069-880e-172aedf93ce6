"""
Market Intelligence Module for Erica's Trading System

Provides comprehensive market analysis, regime detection, sector rotation insights,
and economic indicator interpretation for intelligent trading decisions.
"""

from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from datetime import datetime, timed<PERSON>ta
from enum import Enum
import logging

from strategy_knowledge_base import StrategyKnowledgeBase, MarketRegime
from market_analysis_engine import MarketAnalysisEngine, MarketFactors
from enhanced_fmp_api import EnhancedFMPClient

class MarketTrend(Enum):
    STRONG_BULLISH = "strong_bullish"
    BULLISH = "bullish"
    NEUTRAL = "neutral"
    BEARISH = "bearish"
    STRONG_BEARISH = "strong_bearish"

class VolatilityRegime(Enum):
    VERY_LOW = "very_low"      # VIX < 15
    LOW = "low"                # VIX 15-20
    NORMAL = "normal"          # VIX 20-25
    ELEVATED = "elevated"      # VIX 25-35
    HIGH = "high"              # VIX > 35

@dataclass
class MarketIntelligence:
    """Comprehensive market intelligence snapshot"""
    timestamp: datetime
    
    # Market Regime
    current_regime: MarketRegime
    trend_direction: MarketTrend
    volatility_regime: VolatilityRegime
    regime_confidence: float
    
    # Key Metrics
    vix_level: float
    vix_percentile: float
    spy_trend_strength: float
    market_breadth: float
    
    # Sector Analysis
    leading_sectors: List[str]
    lagging_sectors: List[str]
    sector_rotation_signal: str
    
    # Strategy Environment
    best_strategy_types: List[str]
    strategy_rationale: str
    risk_level: str
    
    # Economic Context
    earnings_season_intensity: float
    fed_meeting_proximity: int
    major_events: List[str]

@dataclass
class SectorAnalysis:
    """Detailed sector rotation analysis"""
    sector_name: str
    performance_1d: float
    performance_5d: float
    performance_1m: float
    relative_strength: float
    trend_direction: str
    rotation_signal: str

class MarketIntelligenceEngine:
    """Advanced market intelligence and analysis engine"""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.market_analyzer = MarketAnalysisEngine(api_key)
        self.strategy_kb = StrategyKnowledgeBase()
        self.logger = logging.getLogger(__name__)
        
        # Cache for expensive calculations
        self._intelligence_cache = None
        self._cache_timestamp = None
        self._cache_duration = 300  # 5 minutes
    
    def get_market_intelligence(self, force_refresh: bool = False) -> MarketIntelligence:
        """Get comprehensive market intelligence snapshot"""
        
        # Check cache
        if (not force_refresh and self._intelligence_cache and 
            self._cache_timestamp and 
            (datetime.now() - self._cache_timestamp).seconds < self._cache_duration):
            return self._intelligence_cache
        
        try:
            # Get market factors
            market_factors = self.market_analyzer.analyze_market_factors()
            
            # Analyze market regime
            regime = self._detect_market_regime(market_factors)
            trend = self._analyze_market_trend(market_factors)
            vol_regime = self._classify_volatility_regime(market_factors.vix_level)
            
            # Sector analysis
            sector_data = self._analyze_sector_rotation(market_factors)
            
            # Strategy recommendations
            strategy_env = self._analyze_strategy_environment(market_factors)
            
            intelligence = MarketIntelligence(
                timestamp=datetime.now(),
                current_regime=regime,
                trend_direction=trend,
                volatility_regime=vol_regime,
                regime_confidence=self._calculate_regime_confidence(market_factors),
                vix_level=market_factors.vix_level,
                vix_percentile=market_factors.vix_percentile,
                spy_trend_strength=market_factors.spy_technical_score,
                market_breadth=market_factors.market_breadth_score,
                leading_sectors=sector_data['leaders'],
                lagging_sectors=sector_data['laggards'],
                sector_rotation_signal=market_factors.sector_rotation_signal,
                best_strategy_types=strategy_env['best_strategies'],
                strategy_rationale=strategy_env['rationale'],
                risk_level=strategy_env['risk_level'],
                earnings_season_intensity=market_factors.earnings_season_intensity,
                fed_meeting_proximity=market_factors.fed_meeting_proximity,
                major_events=market_factors.major_events_this_week or []
            )
            
            # Cache the result
            self._intelligence_cache = intelligence
            self._cache_timestamp = datetime.now()
            
            return intelligence
            
        except Exception as e:
            self.logger.error(f"Error generating market intelligence: {e}")
            return self._get_fallback_intelligence()
    
    def _detect_market_regime(self, factors: MarketFactors) -> MarketRegime:
        """Detect current market regime based on multiple factors"""
        
        # High volatility regime
        if factors.vix_level > 25 or factors.vix_percentile > 70:
            return MarketRegime.HIGH_VOLATILITY
        
        # Low volatility regime
        if factors.vix_level < 18 and factors.vix_percentile < 30:
            return MarketRegime.LOW_VOLATILITY
        
        # Earnings season
        if factors.earnings_season_intensity > 0.6:
            return MarketRegime.EARNINGS_SEASON
        
        # Trending vs range-bound
        if factors.spy_technical_score > 0.7:
            return MarketRegime.TRENDING
        else:
            return MarketRegime.RANGE_BOUND
    
    def _analyze_market_trend(self, factors: MarketFactors) -> MarketTrend:
        """Analyze overall market trend direction and strength"""
        
        bullish_score = factors.bullish_factors
        bearish_score = factors.bearish_factors
        net_bias = bullish_score - bearish_score
        
        if net_bias > 0.4:
            return MarketTrend.STRONG_BULLISH
        elif net_bias > 0.15:
            return MarketTrend.BULLISH
        elif net_bias > -0.15:
            return MarketTrend.NEUTRAL
        elif net_bias > -0.4:
            return MarketTrend.BEARISH
        else:
            return MarketTrend.STRONG_BEARISH
    
    def _classify_volatility_regime(self, vix_level: float) -> VolatilityRegime:
        """Classify current volatility regime"""
        
        if vix_level < 15:
            return VolatilityRegime.VERY_LOW
        elif vix_level < 20:
            return VolatilityRegime.LOW
        elif vix_level < 25:
            return VolatilityRegime.NORMAL
        elif vix_level < 35:
            return VolatilityRegime.ELEVATED
        else:
            return VolatilityRegime.HIGH
    
    def _analyze_sector_rotation(self, factors: MarketFactors) -> Dict[str, List[str]]:
        """Analyze sector rotation patterns"""
        
        # Use existing sector data from market factors
        leaders = factors.relative_strength_leaders or ['Technology', 'Healthcare']
        
        # Determine laggards (opposite of leaders)
        all_sectors = ['Technology', 'Healthcare', 'Financials', 'Energy', 
                      'Consumer Discretionary', 'Industrials', 'Materials', 
                      'Utilities', 'Real Estate', 'Consumer Staples']
        
        laggards = [sector for sector in all_sectors[-3:] if sector not in leaders]
        
        return {
            'leaders': leaders[:3],
            'laggards': laggards[:3]
        }
    
    def _analyze_strategy_environment(self, factors: MarketFactors) -> Dict[str, Any]:
        """Analyze current environment for strategy selection"""
        
        best_strategies = []
        rationale_parts = []
        
        # High volatility favors premium selling
        if factors.vix_percentile > 60:
            best_strategies.extend(['credit_spread', 'premium_selling'])
            rationale_parts.append(f"High IV environment (VIX {factors.vix_level:.1f}) favors premium selling")
        
        # Low volatility favors premium buying
        elif factors.vix_percentile < 40:
            best_strategies.extend(['leaps', 'covered_call'])
            rationale_parts.append(f"Low IV environment (VIX {factors.vix_level:.1f}) favors premium buying")
        
        # Trending markets favor directional strategies
        if factors.spy_technical_score > 0.7:
            best_strategies.append('leaps')
            rationale_parts.append("Strong trend supports directional plays")
        
        # Range-bound markets favor income strategies
        elif factors.spy_technical_score < 0.4:
            best_strategies.extend(['covered_call', 'wheel'])
            rationale_parts.append("Range-bound market supports income strategies")
        
        # Earnings season considerations
        if factors.earnings_season_intensity > 0.5:
            rationale_parts.append("Earnings season - consider conservative positioning")
        
        # Risk level assessment
        risk_level = "Low"
        if factors.vix_level > 25 or factors.uncertainty_factors > 0.6:
            risk_level = "High"
        elif factors.vix_level > 20 or factors.uncertainty_factors > 0.4:
            risk_level = "Moderate"
        
        return {
            'best_strategies': list(set(best_strategies)) or ['covered_call'],
            'rationale': '; '.join(rationale_parts) or "Balanced market conditions",
            'risk_level': risk_level
        }
    
    def _calculate_regime_confidence(self, factors: MarketFactors) -> float:
        """Calculate confidence in regime detection"""
        
        # Higher confidence when multiple indicators align
        confidence = 0.5
        
        # VIX alignment
        if (factors.vix_level > 25 and factors.vix_percentile > 70) or \
           (factors.vix_level < 18 and factors.vix_percentile < 30):
            confidence += 0.2
        
        # Technical alignment
        if factors.spy_technical_score > 0.8 or factors.spy_technical_score < 0.2:
            confidence += 0.2
        
        # Breadth confirmation
        if factors.market_breadth_score > 0.7 or factors.market_breadth_score < 0.3:
            confidence += 0.1
        
        return min(1.0, confidence)
    
    def _get_fallback_intelligence(self) -> MarketIntelligence:
        """Provide fallback intelligence when analysis fails"""
        
        return MarketIntelligence(
            timestamp=datetime.now(),
            current_regime=MarketRegime.RANGE_BOUND,
            trend_direction=MarketTrend.NEUTRAL,
            volatility_regime=VolatilityRegime.NORMAL,
            regime_confidence=0.5,
            vix_level=20.0,
            vix_percentile=50.0,
            spy_trend_strength=0.5,
            market_breadth=0.5,
            leading_sectors=['Technology'],
            lagging_sectors=['Utilities'],
            sector_rotation_signal='neutral',
            best_strategy_types=['covered_call'],
            strategy_rationale='Balanced market conditions',
            risk_level='Moderate',
            earnings_season_intensity=0.3,
            fed_meeting_proximity=30,
            major_events=[]
        )
    
    def answer_market_question(self, question: str) -> str:
        """Answer general market questions using current intelligence"""
        
        intelligence = self.get_market_intelligence()
        question_lower = question.lower()
        
        # Market direction questions
        if any(word in question_lower for word in ['direction', 'trend', 'bullish', 'bearish']):
            return self._answer_trend_question(intelligence)
        
        # Volatility questions
        elif any(word in question_lower for word in ['volatility', 'vix', 'vol']):
            return self._answer_volatility_question(intelligence)
        
        # Strategy questions
        elif any(word in question_lower for word in ['strategy', 'trade', 'recommend']):
            return self._answer_strategy_question(intelligence)
        
        # Sector questions
        elif any(word in question_lower for word in ['sector', 'rotation', 'leading']):
            return self._answer_sector_question(intelligence)
        
        # Risk questions
        elif any(word in question_lower for word in ['risk', 'safe', 'dangerous']):
            return self._answer_risk_question(intelligence)
        
        else:
            return self._provide_general_market_overview(intelligence)
    
    def _answer_trend_question(self, intelligence: MarketIntelligence) -> str:
        """Answer questions about market trend"""
        
        trend_desc = {
            MarketTrend.STRONG_BULLISH: "strongly bullish with high momentum",
            MarketTrend.BULLISH: "bullish with positive momentum", 
            MarketTrend.NEUTRAL: "neutral with mixed signals",
            MarketTrend.BEARISH: "bearish with negative momentum",
            MarketTrend.STRONG_BEARISH: "strongly bearish with high downside momentum"
        }
        
        return (f"The current market trend is {trend_desc[intelligence.trend_direction]}. "
                f"SPY technical strength is at {intelligence.spy_trend_strength:.0%} with "
                f"market breadth at {intelligence.market_breadth:.0%}. "
                f"Leading sectors include {', '.join(intelligence.leading_sectors)}.")
    
    def _answer_volatility_question(self, intelligence: MarketIntelligence) -> str:
        """Answer questions about volatility"""
        
        vol_desc = {
            VolatilityRegime.VERY_LOW: "very low - consider premium buying strategies",
            VolatilityRegime.LOW: "low - favor long options and LEAPS",
            VolatilityRegime.NORMAL: "normal - balanced approach recommended",
            VolatilityRegime.ELEVATED: "elevated - favor premium selling strategies", 
            VolatilityRegime.HIGH: "high - strong premium selling environment"
        }
        
        return (f"Current volatility is {vol_desc[intelligence.volatility_regime]}. "
                f"VIX is at {intelligence.vix_level:.1f} ({intelligence.vix_percentile:.0f}th percentile). "
                f"This environment {intelligence.strategy_rationale.lower()}.")
    
    def _answer_strategy_question(self, intelligence: MarketIntelligence) -> str:
        """Answer questions about trading strategies"""
        
        strategies_text = ', '.join(intelligence.best_strategy_types)
        
        return (f"Based on current market conditions, the best strategies are: {strategies_text}. "
                f"{intelligence.strategy_rationale}. "
                f"Current risk level is {intelligence.risk_level.lower()}.")
    
    def _answer_sector_question(self, intelligence: MarketIntelligence) -> str:
        """Answer questions about sector rotation"""
        
        return (f"Current sector rotation shows {intelligence.sector_rotation_signal} signals. "
                f"Leading sectors: {', '.join(intelligence.leading_sectors)}. "
                f"Lagging sectors: {', '.join(intelligence.lagging_sectors)}. "
                f"This suggests a {intelligence.trend_direction.value.replace('_', ' ')} market bias.")
    
    def _answer_risk_question(self, intelligence: MarketIntelligence) -> str:
        """Answer questions about market risk"""
        
        risk_factors = []
        if intelligence.vix_level > 25:
            risk_factors.append("elevated volatility")
        if intelligence.fed_meeting_proximity <= 7:
            risk_factors.append("upcoming Fed meeting")
        if intelligence.earnings_season_intensity > 0.6:
            risk_factors.append("heavy earnings season")
        
        if risk_factors:
            return (f"Current risk level is {intelligence.risk_level.lower()} due to: "
                   f"{', '.join(risk_factors)}. Consider smaller position sizes and "
                   f"more conservative strategies.")
        else:
            return (f"Current risk level is {intelligence.risk_level.lower()}. "
                   f"Market conditions are relatively stable for normal position sizing.")
    
    def _provide_general_market_overview(self, intelligence: MarketIntelligence) -> str:
        """Provide general market overview"""
        
        return (f"Market Overview: {intelligence.trend_direction.value.replace('_', ' ').title()} trend "
                f"in a {intelligence.volatility_regime.value.replace('_', ' ')} volatility environment. "
                f"VIX: {intelligence.vix_level:.1f}, Risk Level: {intelligence.risk_level}. "
                f"Best strategies: {', '.join(intelligence.best_strategy_types)}. "
                f"{intelligence.strategy_rationale}.")
