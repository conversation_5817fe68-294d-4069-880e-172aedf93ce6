# Troubleshooting Guide - Catalyst Analysis System

## 🔧 Common Issues and Solutions

### Error: "type object 'Strategy...'"

If you encounter an error related to Strategy object types, here are the solutions:

#### **1. Import Issues**
**Problem**: Strategy types not importing correctly
**Solution**: 
```python
# Make sure you're importing from the correct modules
from daily_outline import StrategyType
from enhanced_strategy_analyzer import ExtendedStrategyType

# Check if the import is successful
print("StrategyType imported:", StrategyType.COVERED_CALL)
```

#### **2. Module Conflicts**
**Problem**: Multiple Strategy enums causing conflicts
**Solution**:
```python
# Use explicit imports to avoid conflicts
from daily_outline import StrategyType as BaseStrategyType
from enhanced_strategy_analyzer import ExtendedStrategyType

# Or use fully qualified names
import daily_outline
strategy = daily_outline.StrategyType.COVERED_CALL
```

#### **3. Dataclass Field Ordering**
**Problem**: Optional fields before required fields in dataclasses
**Solution**: All optional fields must be at the end of the dataclass definition

#### **4. API Key Issues**
**Problem**: Missing or invalid API key
**Solution**:
```bash
# Set your FMP API key
export FMP_API_KEY="your_api_key_here"

# Or in Windows
set FMP_API_KEY=your_api_key_here
```

## 🧪 Quick System Test

Run this command to test the system:
```bash
python test_catalyst_system.py
```

Expected output:
```
✅ ALL TESTS PASSED!
🎯 The comprehensive catalyst analysis system is working correctly
```

## 🚀 System Components Status

### **Core Files**
- ✅ `enhanced_strategy_analyzer.py` - Main analysis engine
- ✅ `catalyst_analysis_display.py` - Display formatting
- ✅ `live_market_data_integration.py` - Real-time data
- ✅ `field_guide_strategy_selector.py` - Strategy selection
- ✅ `complete_field_guide_demo.py` - Complete demo
- ✅ `enhanced_catalyst_demo.py` - Catalyst analysis demo

### **Key Features Working**
- ✅ Live market data integration
- ✅ Comprehensive catalyst analysis (4 components)
- ✅ Strategy justification with confidence levels
- ✅ Bullish catalysts identification
- ✅ Risk factors analysis
- ✅ Analyst sentiment and price targets
- ✅ Stock-specific analysis (AAPL, NVDA, AMD, GOOGL, AMZN)
- ✅ Field guide strategy implementation
- ✅ Erica's methodology integration

## 🔍 Debugging Steps

### **Step 1: Check Imports**
```python
try:
    from enhanced_strategy_analyzer import create_enhanced_analyzer
    print("✅ Enhanced analyzer import successful")
except ImportError as e:
    print(f"❌ Import error: {e}")
```

### **Step 2: Check API Key**
```python
from daily_outline import resolve_fmp_key
api_key = resolve_fmp_key(None)
print(f"API Key available: {bool(api_key)}")
```

### **Step 3: Test Basic Analysis**
```python
analyzer = create_enhanced_analyzer(api_key)
analysis = analyzer.analyze_stock_comprehensive("AAPL")
print(f"Analysis completed: {analysis.symbol}")
```

### **Step 4: Check Catalyst Analysis**
```python
if analysis.comprehensive_catalyst_analysis:
    print("✅ Comprehensive catalyst analysis available")
    print(f"Strategy: {analysis.comprehensive_catalyst_analysis.strategy_justification.strategy_name}")
else:
    print("❌ No comprehensive catalyst analysis")
```

## 🛠️ Manual Fixes

### **If Strategy Type Errors Persist**

1. **Clear Python cache**:
```bash
find . -name "*.pyc" -delete
find . -name "__pycache__" -type d -exec rm -rf {} +
```

2. **Restart Python interpreter**:
```python
# Exit and restart your Python session
exit()
```

3. **Check for circular imports**:
```python
import sys
print("Loaded modules:", [m for m in sys.modules.keys() if 'strategy' in m.lower()])
```

### **If Live Data Issues**

1. **Check internet connection**
2. **Verify API key is valid**
3. **Test with fallback data**:
```python
# The system automatically falls back to demo data if API fails
analyzer = create_enhanced_analyzer("test_key")  # Will use fallback data
```

## 📞 Support Commands

### **Quick Health Check**
```bash
python -c "
from enhanced_strategy_analyzer import create_enhanced_analyzer
from daily_outline import resolve_fmp_key
api_key = resolve_fmp_key(None) or 'test'
analyzer = create_enhanced_analyzer(api_key)
analysis = analyzer.analyze_stock_comprehensive('AAPL')
print('✅ System healthy:', bool(analysis.comprehensive_catalyst_analysis))
"
```

### **Full Demo Test**
```bash
# Test the complete system
python enhanced_catalyst_demo.py

# Test field guide integration
python complete_field_guide_demo.py
```

## 🎯 Expected Behavior

When working correctly, the system should:

1. **Import successfully** without errors
2. **Create analyzer** with live data integration
3. **Perform analysis** with comprehensive catalyst analysis
4. **Display results** with all 4 catalyst components:
   - Strategy justification
   - Bullish catalysts
   - Risk factors  
   - Analyst sentiment
5. **Use live data** when API key is available
6. **Fall back gracefully** to demo data when needed

## 📊 System Status Check

Current system status: **✅ OPERATIONAL**

All tests passing as of last check. If you encounter issues:

1. Run `python test_catalyst_system.py`
2. Check the specific error message
3. Follow the debugging steps above
4. Verify API key configuration
5. Restart Python interpreter if needed

The comprehensive catalyst analysis system is fully functional and ready for use with live market data and Erica's proven methodologies.
