"""
Enhanced AI Options Trading Assistant with Comprehensive Market Data Integration
Expert-level options trading analysis for Cash Secured Put positions and options strategies

This module provides a comprehensive AI assistant that can:
- Analyze specific Cash Secured Put positions with full market context
- Access real-time options data including Greeks, IV changes, and volume
- Identify and analyze news catalysts driving stock movements
- Provide expert-level options trading insights and strategic recommendations
- Track portfolio positions with entry details and current P&L
- Integrate with <PERSON>'s methodology for context-aware decisions

Key Features:
- Real-time market data integration (FMP API + Yahoo Finance)
- Options chain analysis with live Greeks calculation
- Advanced news catalyst detection and impact assessment
- Position-specific analysis and recommendations
- Expert options trading knowledge base
- Risk assessment and scenario analysis
- Strategic recommendations based on current conditions
"""

import json
import logging
import requests
from typing import Dict, List, Optional, Any, Tuple, Union
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
try:
    import yfinance as yf
except ImportError:
    yf = None

try:
    import numpy as np
except ImportError:
    np = None

try:
    from scipy.stats import norm
except ImportError:
    norm = None

import math

# Import existing trading system components
from daily_outline import fmp_quote, fmp_historical_daily, fmp_news_today
try:
    from yahoo_options import get_options_chain_yf
except ImportError:
    get_options_chain_yf = None
try:
    from enhanced_strategy_analyzer import DetailedStrategyAnalysis, CatalystAnalysis
except ImportError:
    DetailedStrategyAnalysis = None
    CatalystAnalysis = None

try:
    from market_analysis_engine import MarketAnalysisEngine, MarketFactors, StockSpecificFactors
except ImportError:
    MarketAnalysisEngine = None
    MarketFactors = None
    StockSpecificFactors = None

try:
    from erica_strategy_engine import EricaStrategyEngine, EricaTradeSetup
except ImportError:
    EricaStrategyEngine = None
    EricaTradeSetup = None

try:
    from live_market_data_integration import LiveMarketDataProvider, LiveMarketData
except ImportError:
    LiveMarketDataProvider = None
    LiveMarketData = None

class PositionType(Enum):
    CASH_SECURED_PUT = "cash_secured_put"
    COVERED_CALL = "covered_call"
    PUT_CREDIT_SPREAD = "put_credit_spread"
    CALL_CREDIT_SPREAD = "call_credit_spread"
    IRON_CONDOR = "iron_condor"
    LONG_CALL = "long_call"
    LONG_PUT = "long_put"

class CatalystImpact(Enum):
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"

@dataclass
class OptionsPosition:
    """Represents a specific options position in the portfolio"""
    symbol: str
    position_type: PositionType
    entry_date: datetime
    expiration_date: datetime
    strike_price: float
    entry_price: float
    quantity: int
    current_price: Optional[float] = None
    current_value: Optional[float] = None
    unrealized_pnl: Optional[float] = None
    days_to_expiration: Optional[int] = None
    current_delta: Optional[float] = None
    current_theta: Optional[float] = None
    current_gamma: Optional[float] = None
    current_vega: Optional[float] = None
    iv_at_entry: Optional[float] = None
    current_iv: Optional[float] = None
    original_strategy_rationale: Optional[str] = None

@dataclass
class NewsCatalyst:
    """Represents a specific news catalyst affecting the stock"""
    headline: str
    published_date: datetime
    catalyst_type: str  # earnings, upgrade, partnership, etc.
    impact_level: CatalystImpact
    sentiment_score: float  # -1.0 to 1.0
    relevance_score: float  # 0.0 to 1.0
    summary: str
    source: str
    url: Optional[str] = None

@dataclass
class OptionsAnalysisContext:
    """Comprehensive context for options analysis"""
    symbol: str
    current_price: float
    price_change_percent: float
    volume: int
    avg_volume: int
    iv_rank: float
    iv_percentile: float
    hv_30: float
    iv_30: float
    support_levels: List[float]
    resistance_levels: List[float]
    earnings_date: Optional[datetime]
    earnings_days_away: Optional[int]
    expected_move: Optional[float]
    news_catalysts: List[NewsCatalyst]
    analyst_rating: Optional[str]
    price_target: Optional[float]
    options_volume: int
    put_call_ratio: float
    max_pain: Optional[float]

class EnhancedOptionsAIAssistant:
    """Enhanced AI assistant for comprehensive options trading analysis"""
    
    def __init__(self, fmp_api_key: str, openai_api_key: Optional[str] = None):
        self.fmp_api_key = fmp_api_key
        self.openai_api_key = openai_api_key
        self.logger = logging.getLogger(__name__)

        # Initialize data providers (with fallbacks for missing dependencies)
        self.market_data_provider = None
        if LiveMarketDataProvider:
            try:
                self.market_data_provider = LiveMarketDataProvider(fmp_api_key)
            except Exception as e:
                self.logger.warning(f"Could not initialize LiveMarketDataProvider: {e}")

        self.market_analyzer = None
        if MarketAnalysisEngine:
            try:
                self.market_analyzer = MarketAnalysisEngine(fmp_api_key)
            except Exception as e:
                self.logger.warning(f"Could not initialize MarketAnalysisEngine: {e}")

        self.erica_engine = None
        if EricaStrategyEngine:
            try:
                self.erica_engine = EricaStrategyEngine(fmp_api_key)
            except Exception as e:
                self.logger.warning(f"Could not initialize EricaStrategyEngine: {e}")

        # Portfolio positions (would be loaded from database/file in production)
        self.portfolio_positions: Dict[str, List[OptionsPosition]] = {}

        # Initialize OpenAI client if API key provided
        self.openai_client = None
        if openai_api_key:
            try:
                from openai import OpenAI
                self.openai_client = OpenAI(api_key=openai_api_key)
            except ImportError:
                self.logger.warning("OpenAI package not installed. AI responses will be rule-based.")
    
    def analyze_cash_secured_put_catalyst(self, symbol: str, position_id: Optional[str] = None) -> str:
        """
        Comprehensive analysis of Cash Secured Put position with news catalyst identification
        
        This is the main method that answers: "What is the news catalyst for this Cash Secured Put?"
        """
        try:
            # 1. Get comprehensive market context
            context = self._build_analysis_context(symbol)
            
            # 2. Identify specific position if provided
            position = self._get_position(symbol, position_id) if position_id else None
            
            # 3. Analyze news catalysts
            catalyst_analysis = self._analyze_news_catalysts(context)
            
            # 4. Perform options-specific analysis
            options_analysis = self._analyze_options_metrics(context, position)
            
            # 5. Generate strategic recommendations
            recommendations = self._generate_strategic_recommendations(context, position, catalyst_analysis)
            
            # 6. Format comprehensive response
            response = self._format_comprehensive_response(
                symbol, context, position, catalyst_analysis, options_analysis, recommendations
            )
            
            return response
            
        except Exception as e:
            self.logger.error(f"Error analyzing CSP catalyst for {symbol}: {e}")
            return f"Error analyzing {symbol}: {str(e)}"
    
    def _build_analysis_context(self, symbol: str) -> OptionsAnalysisContext:
        """Build comprehensive analysis context with real-time data"""

        # Get basic quote data as fallback
        quote_data = fmp_quote(symbol, self.fmp_api_key) or {}
        current_price = quote_data.get('price', 100.0)

        # Get live market data if available
        live_data = None
        if self.market_data_provider:
            try:
                live_data = self.market_data_provider.get_live_data(symbol)
                current_price = live_data.current_price
            except Exception as e:
                self.logger.warning(f"Could not get live data for {symbol}: {e}")

        # Get options chain
        options_chain = []
        try:
            options_chain = get_options_chain_yf(symbol, spot_price=current_price, max_expirations=10)
        except Exception as e:
            self.logger.warning(f"Could not get options chain for {symbol}: {e}")

        # Get news data
        news_data = []
        try:
            news_data = fmp_news_today([symbol], self.fmp_api_key, limit=20)
        except Exception as e:
            self.logger.warning(f"Could not get news for {symbol}: {e}")

        # Process news into catalysts
        news_catalysts = self._process_news_catalysts(news_data, symbol)

        # Calculate options metrics
        options_volume = sum(opt.get('volume', 0) for opt in options_chain if opt.get('volume'))
        put_volume = sum(opt.get('volume', 0) for opt in options_chain
                        if opt.get('type') == 'put' and opt.get('volume'))
        call_volume = sum(opt.get('volume', 0) for opt in options_chain
                         if opt.get('type') == 'call' and opt.get('volume'))
        put_call_ratio = put_volume / call_volume if call_volume > 0 else 0

        # Calculate max pain (simplified)
        max_pain = self._calculate_max_pain(options_chain, current_price)

        # Use live data if available, otherwise use defaults
        if live_data:
            return OptionsAnalysisContext(
                symbol=symbol,
                current_price=live_data.current_price,
                price_change_percent=live_data.change_percent,
                volume=live_data.volume,
                avg_volume=live_data.avg_volume,
                iv_rank=live_data.iv_rank,
                iv_percentile=live_data.iv_percentile,
                hv_30=live_data.hv_30,
                iv_30=live_data.iv_30,
                support_levels=[live_data.support_level] if live_data.support_level else [],
                resistance_levels=[live_data.resistance_level] if live_data.resistance_level else [],
                earnings_date=live_data.earnings_date,
                earnings_days_away=live_data.earnings_days_away,
                expected_move=live_data.expected_move,
                news_catalysts=news_catalysts,
                analyst_rating=None,
                price_target=None,
                options_volume=options_volume,
                put_call_ratio=put_call_ratio,
                max_pain=max_pain
            )
        else:
            # Fallback context with basic data
            return OptionsAnalysisContext(
                symbol=symbol,
                current_price=current_price,
                price_change_percent=quote_data.get('changesPercentage', 0.0),
                volume=quote_data.get('volume', 0),
                avg_volume=quote_data.get('avgVolume', 0),
                iv_rank=0.5,  # Default values
                iv_percentile=0.5,
                hv_30=0.25,
                iv_30=0.30,
                support_levels=[],
                resistance_levels=[],
                earnings_date=None,
                earnings_days_away=None,
                expected_move=None,
                news_catalysts=news_catalysts,
                analyst_rating=None,
                price_target=None,
                options_volume=options_volume,
                put_call_ratio=put_call_ratio,
                max_pain=max_pain
            )
    
    def _process_news_catalysts(self, news_data: List[Dict], symbol: str) -> List[NewsCatalyst]:
        """Process raw news data into structured catalyst objects"""
        catalysts = []
        
        for article in news_data:
            try:
                headline = article.get('title', '')
                text = article.get('text', '')
                published_str = article.get('publishedDate', '')
                
                # Parse date
                try:
                    published_date = datetime.fromisoformat(published_str.replace('Z', '+00:00'))
                except:
                    published_date = datetime.now()
                
                # Determine catalyst type and impact
                catalyst_type, impact_level = self._classify_news_catalyst(headline, text)
                
                # Calculate sentiment and relevance scores
                sentiment_score = self._calculate_sentiment_score(headline, text)
                relevance_score = self._calculate_relevance_score(headline, text, symbol)
                
                if relevance_score > 0.3:  # Only include relevant news
                    catalyst = NewsCatalyst(
                        headline=headline,
                        published_date=published_date,
                        catalyst_type=catalyst_type,
                        impact_level=impact_level,
                        sentiment_score=sentiment_score,
                        relevance_score=relevance_score,
                        summary=text[:200] + "..." if len(text) > 200 else text,
                        source=article.get('site', 'Unknown'),
                        url=article.get('url')
                    )
                    catalysts.append(catalyst)
                    
            except Exception as e:
                self.logger.warning(f"Error processing news article: {e}")
                continue
        
        # Sort by relevance and recency
        catalysts.sort(key=lambda x: (x.relevance_score, x.published_date), reverse=True)
        return catalysts[:10]  # Return top 10 most relevant catalysts

    def _classify_news_catalyst(self, headline: str, text: str) -> Tuple[str, CatalystImpact]:
        """Classify news catalyst type and impact level"""
        content = (headline + " " + text).lower()

        # High impact catalysts
        if any(keyword in content for keyword in ['earnings', 'beat', 'miss', 'guidance', 'outlook']):
            return "earnings", CatalystImpact.HIGH
        elif any(keyword in content for keyword in ['upgrade', 'downgrade', 'initiated', 'price target']):
            return "analyst_rating", CatalystImpact.HIGH
        elif any(keyword in content for keyword in ['fda approval', 'merger', 'acquisition', 'partnership']):
            return "corporate_action", CatalystImpact.HIGH
        elif any(keyword in content for keyword in ['lawsuit', 'investigation', 'regulatory']):
            return "regulatory", CatalystImpact.HIGH

        # Medium impact catalysts
        elif any(keyword in content for keyword in ['product launch', 'contract', 'deal', 'expansion']):
            return "business_development", CatalystImpact.MEDIUM
        elif any(keyword in content for keyword in ['management', 'ceo', 'executive', 'leadership']):
            return "management", CatalystImpact.MEDIUM
        elif any(keyword in content for keyword in ['dividend', 'buyback', 'split']):
            return "capital_allocation", CatalystImpact.MEDIUM

        # Low impact catalysts
        else:
            return "general", CatalystImpact.LOW

    def _calculate_sentiment_score(self, headline: str, text: str) -> float:
        """Calculate sentiment score from -1.0 (bearish) to 1.0 (bullish)"""
        content = (headline + " " + text).lower()

        positive_words = ['beat', 'exceed', 'strong', 'growth', 'upgrade', 'buy', 'outperform',
                         'positive', 'bullish', 'optimistic', 'record', 'high', 'success']
        negative_words = ['miss', 'weak', 'decline', 'downgrade', 'sell', 'underperform',
                         'negative', 'bearish', 'pessimistic', 'low', 'concern', 'risk']

        positive_count = sum(1 for word in positive_words if word in content)
        negative_count = sum(1 for word in negative_words if word in content)

        if positive_count + negative_count == 0:
            return 0.0

        return (positive_count - negative_count) / (positive_count + negative_count)

    def _calculate_relevance_score(self, headline: str, text: str, symbol: str) -> float:
        """Calculate relevance score from 0.0 to 1.0"""
        content = (headline + " " + text).lower()
        symbol_lower = symbol.lower()

        # Direct symbol mention
        if symbol_lower in content:
            return 1.0

        # Company name mentions (simplified - would use company name lookup in production)
        company_names = {
            'aapl': ['apple', 'iphone', 'ipad', 'mac'],
            'nvda': ['nvidia', 'gpu', 'ai chip', 'data center'],
            'amd': ['amd', 'ryzen', 'radeon', 'epyc'],
            'googl': ['google', 'alphabet', 'search', 'youtube'],
            'amzn': ['amazon', 'aws', 'prime', 'alexa']
        }

        if symbol_lower in company_names:
            for name in company_names[symbol_lower]:
                if name in content:
                    return 0.8

        # Sector-related mentions
        sector_keywords = ['tech', 'technology', 'semiconductor', 'cloud', 'ai', 'artificial intelligence']
        if any(keyword in content for keyword in sector_keywords):
            return 0.4

        return 0.1  # Minimal relevance for general market news

    def _calculate_max_pain(self, options_chain: List[Dict], current_price: float) -> Optional[float]:
        """Calculate max pain level (strike with maximum open interest)"""
        try:
            strike_oi = {}

            for option in options_chain:
                strike = option.get('strike')
                oi = option.get('openInterest', 0)

                if strike and oi:
                    if strike not in strike_oi:
                        strike_oi[strike] = 0
                    strike_oi[strike] += oi

            if not strike_oi:
                return None

            # Find strike with maximum open interest
            max_pain_strike = max(strike_oi.keys(), key=lambda x: strike_oi[x])
            return max_pain_strike

        except Exception as e:
            self.logger.warning(f"Error calculating max pain: {e}")
            return None

    def _get_position(self, symbol: str, position_id: Optional[str]) -> Optional[OptionsPosition]:
        """Get specific options position from portfolio"""
        if symbol not in self.portfolio_positions:
            return None

        positions = self.portfolio_positions[symbol]

        if position_id:
            # Find specific position by ID (simplified - would use actual position ID)
            return positions[0] if positions else None
        else:
            # Return most recent CSP position
            csp_positions = [p for p in positions if p.position_type == PositionType.CASH_SECURED_PUT]
            return csp_positions[-1] if csp_positions else None

    def _analyze_news_catalysts(self, context: OptionsAnalysisContext) -> Dict[str, Any]:
        """Analyze news catalysts and their impact on the position"""
        analysis = {
            'primary_catalyst': None,
            'catalyst_summary': '',
            'impact_assessment': 'neutral',
            'timing_analysis': '',
            'strategic_implications': []
        }

        if not context.news_catalysts:
            analysis['catalyst_summary'] = "No significant news catalysts identified in recent period."
            return analysis

        # Identify primary catalyst (highest impact and relevance)
        primary_catalyst = max(context.news_catalysts,
                             key=lambda x: (x.impact_level.value == 'high', x.relevance_score))
        analysis['primary_catalyst'] = primary_catalyst

        # Assess overall impact
        high_impact_catalysts = [c for c in context.news_catalysts if c.impact_level == CatalystImpact.HIGH]
        sentiment_scores = [c.sentiment_score for c in context.news_catalysts]
        avg_sentiment = sum(sentiment_scores) / len(sentiment_scores) if sentiment_scores else 0.0

        if len(high_impact_catalysts) > 0 and avg_sentiment > 0.3:
            analysis['impact_assessment'] = 'bullish'
        elif len(high_impact_catalysts) > 0 and avg_sentiment < -0.3:
            analysis['impact_assessment'] = 'bearish'
        else:
            analysis['impact_assessment'] = 'neutral'

        # Generate catalyst summary
        analysis['catalyst_summary'] = self._generate_catalyst_summary(context.news_catalysts, primary_catalyst)

        # Timing analysis
        recent_catalysts = [c for c in context.news_catalysts
                          if (datetime.now() - c.published_date).days <= 1]
        if recent_catalysts:
            analysis['timing_analysis'] = f"{len(recent_catalysts)} catalyst(s) in last 24 hours"
        else:
            analysis['timing_analysis'] = "No immediate catalysts, market moving on broader sentiment"

        # Strategic implications for options
        analysis['strategic_implications'] = self._assess_catalyst_implications(
            context.news_catalysts, context.iv_rank, context.earnings_days_away
        )

        return analysis

    def _generate_catalyst_summary(self, catalysts: List[NewsCatalyst], primary_catalyst: NewsCatalyst) -> str:
        """Generate human-readable catalyst summary"""
        if not catalysts:
            return "No significant catalysts identified."

        summary = f"PRIMARY CATALYST: {primary_catalyst.headline}\n"
        summary += f"Type: {primary_catalyst.catalyst_type.replace('_', ' ').title()}\n"
        summary += f"Impact: {primary_catalyst.impact_level.value.title()}\n"
        summary += f"Sentiment: {'Bullish' if primary_catalyst.sentiment_score > 0.1 else 'Bearish' if primary_catalyst.sentiment_score < -0.1 else 'Neutral'}\n"

        if len(catalysts) > 1:
            other_catalysts = [c for c in catalysts if c != primary_catalyst][:3]
            summary += f"\nOTHER CATALYSTS:\n"
            for i, catalyst in enumerate(other_catalysts, 1):
                summary += f"{i}. {catalyst.headline[:80]}...\n"

        return summary

    def _assess_catalyst_implications(self, catalysts: List[NewsCatalyst], iv_rank: float,
                                   earnings_days_away: Optional[int]) -> List[str]:
        """Assess strategic implications of catalysts for options trading"""
        implications = []

        # IV implications
        if iv_rank > 0.7:
            implications.append("High IV rank suggests premium selling strategies may be favorable")
        elif iv_rank < 0.3:
            implications.append("Low IV rank suggests directional strategies may be more attractive")

        # Earnings implications
        if earnings_days_away and earnings_days_away <= 7:
            implications.append("Earnings announcement imminent - expect IV crush post-earnings")
            implications.append("Consider closing positions before earnings or sizing down")

        # Catalyst-specific implications
        high_impact_catalysts = [c for c in catalysts if c.impact_level == CatalystImpact.HIGH]
        if high_impact_catalysts:
            sentiment_scores = [c.sentiment_score for c in high_impact_catalysts]
            avg_sentiment = sum(sentiment_scores) / len(sentiment_scores) if sentiment_scores else 0.0
            if avg_sentiment > 0.3:
                implications.append("Bullish catalysts may support put selling strategies")
            elif avg_sentiment < -0.3:
                implications.append("Bearish catalysts increase assignment risk for cash-secured puts")

        return implications

    def _analyze_options_metrics(self, context: OptionsAnalysisContext,
                               position: Optional[OptionsPosition]) -> Dict[str, Any]:
        """Analyze options-specific metrics and Greeks"""
        analysis = {
            'iv_analysis': {},
            'volume_analysis': {},
            'greeks_analysis': {},
            'position_analysis': {}
        }

        # IV Analysis
        iv_change = context.iv_30 - context.hv_30 if context.hv_30 else 0
        analysis['iv_analysis'] = {
            'current_iv_rank': context.iv_rank,
            'iv_percentile': context.iv_percentile,
            'iv_vs_hv': iv_change,
            'iv_assessment': self._assess_iv_environment(context.iv_rank, iv_change)
        }

        # Volume Analysis
        volume_ratio = context.volume / context.avg_volume if context.avg_volume > 0 else 1.0
        analysis['volume_analysis'] = {
            'volume_ratio': volume_ratio,
            'options_volume': context.options_volume,
            'put_call_ratio': context.put_call_ratio,
            'volume_assessment': self._assess_volume_environment(volume_ratio, context.put_call_ratio)
        }

        # Position-specific analysis
        if position:
            analysis['position_analysis'] = self._analyze_specific_position(position, context)

        return analysis

    def _assess_iv_environment(self, iv_rank: float, iv_vs_hv: float) -> str:
        """Assess implied volatility environment"""
        if iv_rank > 0.8:
            return "Extremely high IV - excellent for premium selling"
        elif iv_rank > 0.6:
            return "High IV - favorable for premium selling strategies"
        elif iv_rank > 0.4:
            return "Moderate IV - neutral environment"
        elif iv_rank > 0.2:
            return "Low IV - consider directional strategies"
        else:
            return "Very low IV - poor environment for premium selling"

    def _assess_volume_environment(self, volume_ratio: float, put_call_ratio: float) -> str:
        """Assess volume and flow environment"""
        assessment = []

        if volume_ratio > 2.0:
            assessment.append("High volume suggests institutional interest")
        elif volume_ratio < 0.5:
            assessment.append("Low volume - limited liquidity")

        if put_call_ratio > 1.5:
            assessment.append("Heavy put buying - bearish sentiment")
        elif put_call_ratio < 0.5:
            assessment.append("Heavy call buying - bullish sentiment")
        else:
            assessment.append("Balanced put/call flow")

        return "; ".join(assessment) if assessment else "Normal volume environment"

    def _analyze_specific_position(self, position: OptionsPosition,
                                 context: OptionsAnalysisContext) -> Dict[str, Any]:
        """Analyze specific options position"""
        analysis = {
            'days_to_expiration': position.days_to_expiration,
            'moneyness': position.strike_price / context.current_price,
            'time_decay_risk': 'high' if position.days_to_expiration and position.days_to_expiration < 7 else 'moderate',
            'assignment_risk': self._assess_assignment_risk(position, context),
            'profit_loss_analysis': self._calculate_position_pnl(position, context),
            'strategic_recommendations': []
        }

        # Add strategic recommendations based on position analysis
        if analysis['assignment_risk'] == 'high':
            analysis['strategic_recommendations'].append("Consider rolling position to avoid assignment")

        if analysis['time_decay_risk'] == 'high':
            analysis['strategic_recommendations'].append("Time decay accelerating - consider closing if profitable")

        return analysis

    def _assess_assignment_risk(self, position: OptionsPosition, context: OptionsAnalysisContext) -> str:
        """Assess assignment risk for short options positions"""
        if position.position_type != PositionType.CASH_SECURED_PUT:
            return "N/A"

        moneyness = position.strike_price / context.current_price

        if moneyness > 1.05:  # Strike > 5% above current price
            return "low"
        elif moneyness > 1.02:  # Strike > 2% above current price
            return "moderate"
        else:
            return "high"

    def _calculate_position_pnl(self, position: OptionsPosition, context: OptionsAnalysisContext) -> Dict[str, float]:
        """Calculate position P&L metrics"""
        if not position.current_price:
            return {'unrealized_pnl': 0.0, 'percent_return': 0.0}

        if position.position_type == PositionType.CASH_SECURED_PUT:
            # For short puts, profit when option price decreases
            unrealized_pnl = (position.entry_price - position.current_price) * position.quantity * 100
        else:
            # For long positions, profit when option price increases
            unrealized_pnl = (position.current_price - position.entry_price) * position.quantity * 100

        percent_return = (unrealized_pnl / (position.entry_price * position.quantity * 100)) * 100

        return {
            'unrealized_pnl': unrealized_pnl,
            'percent_return': percent_return
        }

    def _generate_strategic_recommendations(self, context: OptionsAnalysisContext,
                                          position: Optional[OptionsPosition],
                                          catalyst_analysis: Dict[str, Any]) -> List[str]:
        """Generate strategic recommendations based on comprehensive analysis"""
        recommendations = []

        # Position-specific recommendations
        if position:
            if position.position_type == PositionType.CASH_SECURED_PUT:
                recommendations.extend(self._get_csp_recommendations(position, context, catalyst_analysis))

        # Market environment recommendations
        if context.iv_rank > 0.7:
            recommendations.append("High IV environment favors premium selling strategies")
        elif context.iv_rank < 0.3:
            recommendations.append("Low IV environment - consider directional plays or wait for volatility expansion")

        # Earnings-based recommendations
        if context.earnings_days_away and context.earnings_days_away <= 7:
            recommendations.append("Earnings imminent - consider position sizing and IV crush risk")

        # Catalyst-based recommendations
        if catalyst_analysis['impact_assessment'] == 'bearish':
            recommendations.append("Bearish catalysts suggest defensive positioning or profit-taking")
        elif catalyst_analysis['impact_assessment'] == 'bullish':
            recommendations.append("Bullish catalysts support maintaining or adding to positions")

        return recommendations

    def _get_csp_recommendations(self, position: OptionsPosition, context: OptionsAnalysisContext,
                               catalyst_analysis: Dict[str, Any]) -> List[str]:
        """Get specific recommendations for Cash Secured Put positions"""
        recommendations = []

        # Assignment risk management
        moneyness = position.strike_price / context.current_price
        if moneyness > 1.02:  # Strike more than 2% above current price
            recommendations.append("CSP is at risk of assignment - consider rolling down and out")

        # Time decay considerations
        if position.days_to_expiration and position.days_to_expiration < 7:
            recommendations.append("Rapid time decay - consider closing if 50%+ of max profit achieved")

        # IV considerations
        if context.iv_rank < 0.3 and position.current_price:
            recommendations.append("Low IV may limit further premium decay - consider closing")

        # Catalyst-specific recommendations
        if catalyst_analysis['impact_assessment'] == 'bearish':
            recommendations.append("Bearish catalysts increase assignment risk - consider defensive action")

        return recommendations

    def _format_comprehensive_response(self, symbol: str, context: OptionsAnalysisContext,
                                     position: Optional[OptionsPosition],
                                     catalyst_analysis: Dict[str, Any],
                                     options_analysis: Dict[str, Any],
                                     recommendations: List[str]) -> str:
        """Format comprehensive response for the user"""

        response_parts = []

        # Header
        response_parts.append(f"🎯 COMPREHENSIVE OPTIONS ANALYSIS - {symbol}")
        response_parts.append("=" * 60)

        # Current Market Snapshot
        response_parts.append(f"\n📊 MARKET SNAPSHOT:")
        response_parts.append(f"Current Price: ${context.current_price:.2f} ({context.price_change_percent:+.2f}%)")
        response_parts.append(f"Volume: {context.volume:,} ({context.volume/context.avg_volume:.1f}x avg)")
        response_parts.append(f"IV Rank: {context.iv_rank:.0%} | IV Percentile: {context.iv_percentile:.0%}")

        # News Catalyst Analysis
        response_parts.append(f"\n🚨 NEWS CATALYST ANALYSIS:")
        response_parts.append(catalyst_analysis['catalyst_summary'])
        response_parts.append(f"Overall Impact: {catalyst_analysis['impact_assessment'].upper()}")
        response_parts.append(f"Timing: {catalyst_analysis['timing_analysis']}")

        # Position Analysis (if applicable)
        if position:
            response_parts.append(f"\n💼 POSITION ANALYSIS:")
            response_parts.append(f"Strategy: {position.position_type.value.replace('_', ' ').title()}")
            response_parts.append(f"Strike: ${position.strike_price:.2f} | Expiration: {position.expiration_date.strftime('%Y-%m-%d')}")
            response_parts.append(f"Days to Expiration: {position.days_to_expiration}")

            if 'position_analysis' in options_analysis:
                pos_analysis = options_analysis['position_analysis']
                response_parts.append(f"Assignment Risk: {pos_analysis['assignment_risk'].upper()}")
                if 'profit_loss_analysis' in pos_analysis:
                    pnl = pos_analysis['profit_loss_analysis']
                    response_parts.append(f"Unrealized P&L: ${pnl['unrealized_pnl']:.2f} ({pnl['percent_return']:+.1f}%)")

        # Options Environment Analysis
        response_parts.append(f"\n⚡ OPTIONS ENVIRONMENT:")
        iv_analysis = options_analysis['iv_analysis']
        response_parts.append(f"IV Assessment: {iv_analysis['iv_assessment']}")

        volume_analysis = options_analysis['volume_analysis']
        response_parts.append(f"Volume Assessment: {volume_analysis['volume_assessment']}")
        response_parts.append(f"Put/Call Ratio: {context.put_call_ratio:.2f}")

        if context.max_pain:
            response_parts.append(f"Max Pain Level: ${context.max_pain:.2f}")

        # Strategic Implications
        if catalyst_analysis['strategic_implications']:
            response_parts.append(f"\n🎯 STRATEGIC IMPLICATIONS:")
            for implication in catalyst_analysis['strategic_implications']:
                response_parts.append(f"• {implication}")

        # Recommendations
        response_parts.append(f"\n💡 STRATEGIC RECOMMENDATIONS:")
        for i, rec in enumerate(recommendations, 1):
            response_parts.append(f"{i}. {rec}")

        # Risk Factors
        response_parts.append(f"\n⚠️  KEY RISK FACTORS:")
        if context.earnings_days_away and context.earnings_days_away <= 10:
            response_parts.append(f"• Earnings in {context.earnings_days_away} days - IV crush risk")

        if context.iv_rank > 0.8:
            response_parts.append("• Extremely high IV - mean reversion risk")
        elif context.iv_rank < 0.2:
            response_parts.append("• Very low IV - limited premium collection potential")

        if catalyst_analysis['impact_assessment'] == 'bearish':
            response_parts.append("• Bearish catalysts present - downside risk elevated")

        # Technical Levels
        if context.support_levels or context.resistance_levels:
            response_parts.append(f"\n📈 KEY TECHNICAL LEVELS:")
            if context.support_levels:
                response_parts.append(f"Support: ${context.support_levels[0]:.2f}")
            if context.resistance_levels:
                response_parts.append(f"Resistance: ${context.resistance_levels[0]:.2f}")

        return "\n".join(response_parts)

    def add_portfolio_position(self, position: OptionsPosition):
        """Add a position to the portfolio tracking"""
        if position.symbol not in self.portfolio_positions:
            self.portfolio_positions[position.symbol] = []

        self.portfolio_positions[position.symbol].append(position)
        self.logger.info(f"Added {position.position_type.value} position for {position.symbol}")

    def update_position_prices(self, symbol: str):
        """Update current prices for all positions in a symbol"""
        if symbol not in self.portfolio_positions:
            return

        try:
            # Get current options chain
            live_data = self.market_data_provider.get_live_data(symbol)
            options_chain = get_options_chain_yf(symbol, spot_price=live_data.current_price, max_expirations=10)

            for position in self.portfolio_positions[symbol]:
                # Find matching option in chain
                matching_option = self._find_matching_option(position, options_chain)
                if matching_option:
                    position.current_price = matching_option.get('mid', matching_option.get('bid', 0))
                    position.current_delta = matching_option.get('delta')
                    position.current_theta = matching_option.get('theta')
                    position.current_gamma = matching_option.get('gamma')
                    position.current_vega = matching_option.get('vega')
                    position.current_iv = matching_option.get('iv')

                    # Calculate days to expiration
                    if position.expiration_date:
                        position.days_to_expiration = (position.expiration_date - datetime.now()).days

                    # Calculate current value and P&L
                    if position.current_price:
                        position.current_value = position.current_price * position.quantity * 100
                        if position.position_type == PositionType.CASH_SECURED_PUT:
                            position.unrealized_pnl = (position.entry_price - position.current_price) * position.quantity * 100
                        else:
                            position.unrealized_pnl = (position.current_price - position.entry_price) * position.quantity * 100

        except Exception as e:
            self.logger.error(f"Error updating position prices for {symbol}: {e}")

    def _find_matching_option(self, position: OptionsPosition, options_chain: List[Dict]) -> Optional[Dict]:
        """Find matching option in options chain"""
        option_type = 'put' if position.position_type == PositionType.CASH_SECURED_PUT else 'call'

        for option in options_chain:
            if (option.get('type') == option_type and
                abs(option.get('strike', 0) - position.strike_price) < 0.01 and
                option.get('expiration_str') == position.expiration_date.strftime('%Y-%m-%d')):
                return option

        return None

    def get_portfolio_summary(self) -> str:
        """Get summary of all portfolio positions"""
        if not self.portfolio_positions:
            return "No positions in portfolio."

        summary_parts = []
        summary_parts.append("📊 PORTFOLIO SUMMARY")
        summary_parts.append("=" * 40)

        total_positions = 0
        total_unrealized_pnl = 0.0

        for symbol, positions in self.portfolio_positions.items():
            summary_parts.append(f"\n{symbol}:")
            for i, position in enumerate(positions, 1):
                total_positions += 1
                if position.unrealized_pnl:
                    total_unrealized_pnl += position.unrealized_pnl

                pnl_str = f"${position.unrealized_pnl:.2f}" if position.unrealized_pnl else "N/A"
                summary_parts.append(f"  {i}. {position.position_type.value} ${position.strike_price:.2f} "
                                   f"exp {position.expiration_date.strftime('%m/%d')} - P&L: {pnl_str}")

        summary_parts.append(f"\nTotal Positions: {total_positions}")
        summary_parts.append(f"Total Unrealized P&L: ${total_unrealized_pnl:.2f}")

        return "\n".join(summary_parts)

    def analyze_portfolio_risk(self) -> Dict[str, Any]:
        """Analyze overall portfolio risk metrics"""
        risk_analysis = {
            'total_positions': 0,
            'symbols_count': len(self.portfolio_positions),
            'position_types': {},
            'expiration_distribution': {},
            'assignment_risk_positions': 0,
            'high_iv_positions': 0,
            'near_expiration_positions': 0
        }

        for symbol, positions in self.portfolio_positions.items():
            for position in positions:
                risk_analysis['total_positions'] += 1

                # Position type distribution
                pos_type = position.position_type.value
                risk_analysis['position_types'][pos_type] = risk_analysis['position_types'].get(pos_type, 0) + 1

                # Expiration distribution
                if position.days_to_expiration:
                    if position.days_to_expiration <= 7:
                        risk_analysis['near_expiration_positions'] += 1
                        exp_bucket = "0-7 days"
                    elif position.days_to_expiration <= 21:
                        exp_bucket = "8-21 days"
                    elif position.days_to_expiration <= 45:
                        exp_bucket = "22-45 days"
                    else:
                        exp_bucket = "45+ days"

                    risk_analysis['expiration_distribution'][exp_bucket] = \
                        risk_analysis['expiration_distribution'].get(exp_bucket, 0) + 1

        return risk_analysis
