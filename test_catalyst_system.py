"""
Test Catalyst Analysis System
Quick test to verify the comprehensive catalyst analysis system is working

This script tests:
1. Enhanced strategy analyzer initialization
2. Comprehensive catalyst analysis generation
3. Display formatting
4. Error handling

Usage: python test_catalyst_system.py
"""

import sys
import traceback
from datetime import datetime

def test_system_imports():
    """Test all system imports"""
    print("🔍 Testing system imports...")
    
    try:
        from enhanced_strategy_analyzer import create_enhanced_analyzer, AnalysisDepth
        print("✅ Enhanced strategy analyzer import successful")
    except Exception as e:
        print(f"❌ Enhanced strategy analyzer import failed: {str(e)}")
        return False
    
    try:
        from catalyst_analysis_display import display_comprehensive_catalyst_analysis
        print("✅ Catalyst display import successful")
    except Exception as e:
        print(f"❌ Catalyst display import failed: {str(e)}")
        return False
    
    try:
        from daily_outline import resolve_fmp_key
        print("✅ Daily outline import successful")
    except Exception as e:
        print(f"❌ Daily outline import failed: {str(e)}")
        return False
    
    return True

def test_analyzer_creation():
    """Test analyzer creation"""
    print("\n🔍 Testing analyzer creation...")
    
    try:
        from enhanced_strategy_analyzer import create_enhanced_analyzer
        from daily_outline import resolve_fmp_key
        
        api_key = resolve_fmp_key(None)
        if not api_key:
            print("⚠️  No API key found, using test mode")
            api_key = "test_key"
        
        analyzer = create_enhanced_analyzer(api_key)
        print("✅ Analyzer created successfully")
        return analyzer
    except Exception as e:
        print(f"❌ Analyzer creation failed: {str(e)}")
        traceback.print_exc()
        return None

def test_basic_analysis(analyzer):
    """Test basic analysis functionality"""
    print("\n🔍 Testing basic analysis...")
    
    if not analyzer:
        print("❌ No analyzer available for testing")
        return None
    
    try:
        from enhanced_strategy_analyzer import AnalysisDepth
        
        # Test with a simple symbol
        analysis = analyzer.analyze_stock_comprehensive(
            symbol="AAPL",
            analysis_depth=AnalysisDepth.COMPREHENSIVE
        )
        
        print("✅ Basic analysis completed successfully")
        print(f"   Strategy: {analysis.primary_strategy.value}")
        print(f"   Confidence: {analysis.strategy_confidence.value}")
        
        return analysis
    except Exception as e:
        print(f"❌ Basic analysis failed: {str(e)}")
        traceback.print_exc()
        return None

def test_catalyst_analysis(analysis):
    """Test catalyst analysis display"""
    print("\n🔍 Testing catalyst analysis display...")
    
    if not analysis:
        print("❌ No analysis available for testing")
        return False
    
    try:
        from catalyst_analysis_display import display_comprehensive_catalyst_analysis
        
        if analysis.comprehensive_catalyst_analysis:
            catalyst_display = display_comprehensive_catalyst_analysis(analysis)
            print("✅ Catalyst analysis display successful")
            print(f"   Display length: {len(catalyst_display)} characters")
            
            # Show first few lines
            lines = catalyst_display.split('\n')[:5]
            print("   Preview:")
            for line in lines:
                print(f"     {line}")
            
            return True
        else:
            print("⚠️  No comprehensive catalyst analysis available")
            return False
    except Exception as e:
        print(f"❌ Catalyst analysis display failed: {str(e)}")
        traceback.print_exc()
        return False

def test_data_structures():
    """Test data structure creation"""
    print("\n🔍 Testing data structures...")
    
    try:
        from enhanced_strategy_analyzer import (
            StrategyJustification, BullishCatalyst, RiskFactor, 
            AnalystSentiment, ComprehensiveCatalystAnalysis
        )
        
        # Test creating data structures
        justification = StrategyJustification(
            strategy_name="Test Strategy",
            primary_reasoning="Test reasoning",
            confidence_level="HIGH",
            supporting_factors=["Factor 1", "Factor 2"],
            market_conditions={"iv_rank": 0.5},
            erica_methodology_reference="Test reference"
        )
        
        catalyst = BullishCatalyst(
            catalyst_name="Test Catalyst",
            description="Test description",
            impact_assessment="High",
            catalyst_type="Fundamental",
            timeframe="Short-term",
            supporting_data=["Data 1", "Data 2"]
        )
        
        risk = RiskFactor(
            risk_name="Test Risk",
            description="Test risk description",
            severity="Medium",
            risk_type="Market",
            mitigation_strategy="Test mitigation",
            probability="Low"
        )
        
        sentiment = AnalystSentiment(
            consensus_rating="Buy",
            average_price_target=100.0,
            upside_downside_percent=5.0,
            number_of_analysts=10,
            recent_changes=["Upgrade"],
            target_range=(90.0, 110.0)
        )
        
        comprehensive = ComprehensiveCatalystAnalysis(
            symbol="TEST",
            analysis_date=datetime.now(),
            strategy_justification=justification,
            bullish_catalysts=[catalyst],
            risk_factors=[risk],
            analyst_sentiment=sentiment,
            overall_outlook="BULLISH"
        )
        
        print("✅ Data structures created successfully")
        print(f"   Comprehensive analysis symbol: {comprehensive.symbol}")
        print(f"   Strategy: {comprehensive.strategy_justification.strategy_name}")
        print(f"   Catalysts: {len(comprehensive.bullish_catalysts)}")
        print(f"   Risks: {len(comprehensive.risk_factors)}")
        
        return True
    except Exception as e:
        print(f"❌ Data structure creation failed: {str(e)}")
        traceback.print_exc()
        return False

def run_comprehensive_test():
    """Run comprehensive system test"""
    print("🚀 CATALYST ANALYSIS SYSTEM TEST")
    print("=" * 60)
    
    # Test imports
    if not test_system_imports():
        print("\n❌ Import test failed - stopping")
        return False
    
    # Test analyzer creation
    analyzer = test_analyzer_creation()
    if not analyzer:
        print("\n❌ Analyzer creation failed - stopping")
        return False
    
    # Test data structures
    if not test_data_structures():
        print("\n❌ Data structure test failed - continuing anyway")
    
    # Test basic analysis
    analysis = test_basic_analysis(analyzer)
    if not analysis:
        print("\n❌ Basic analysis failed - stopping")
        return False
    
    # Test catalyst analysis
    if not test_catalyst_analysis(analysis):
        print("\n❌ Catalyst analysis test failed")
        return False
    
    print("\n✅ ALL TESTS PASSED!")
    print("🎯 The comprehensive catalyst analysis system is working correctly")
    
    return True

if __name__ == "__main__":
    success = run_comprehensive_test()
    
    if success:
        print("\n🌟 SYSTEM STATUS: OPERATIONAL")
        print("You can now use:")
        print("  • python enhanced_catalyst_demo.py")
        print("  • python complete_field_guide_demo.py")
        print("  • Enhanced strategy analyzer with catalyst analysis")
    else:
        print("\n⚠️  SYSTEM STATUS: ISSUES DETECTED")
        print("Please check the error messages above")
    
    sys.exit(0 if success else 1)
