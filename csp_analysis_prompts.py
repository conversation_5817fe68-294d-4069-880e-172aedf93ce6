"""
Cash Secured Put Analysis Prompts
Specialized prompts for AI assistant to handle CSP-specific questions

This module provides expert-level prompts and response templates for:
- News catalyst analysis for CSP positions
- Technical analysis integration
- Risk assessment and management
- Strategic recommendations
- <PERSON>'s methodology compliance
"""

from typing import Dict, List, Optional, Any
from datetime import datetime

class CSPAnalysisPrompts:
    """Specialized prompts for Cash Secured Put analysis"""
    
    @staticmethod
    def get_news_catalyst_prompt(symbol: str, analysis_data: Dict[str, Any]) -> str:
        """Generate prompt for news catalyst analysis"""
        
        csp_data = analysis_data.get('comprehensive_csp_analysis', {}).get(symbol, {})
        position_details = csp_data.get('position_details', {})
        news_catalysts = csp_data.get('news_catalysts', [])
        
        prompt = f"""
CASH SECURED PUT CATALYST ANALYSIS REQUEST

Position Details:
- Symbol: {symbol}
- Strike Price: ${position_details.get('strike_price', 'N/A')}
- Days to Expiration: {position_details.get('days_to_expiration', 'N/A')}
- Current P&L: ${position_details.get('unrealized_pnl', 'N/A')} ({position_details.get('pnl_percentage', 'N/A')}%)
- Delta: {position_details.get('delta', 'N/A')}
- Probability of Profit: {position_details.get('probability_of_profit', 'N/A'):.1%}

Recent News Catalysts:
"""
        
        for i, catalyst in enumerate(news_catalysts[:5], 1):
            prompt += f"""
{i}. {catalyst.get('title', 'N/A')}
   Impact: {catalyst.get('impact', 'N/A')}
   Confidence: {catalyst.get('confidence', 'N/A'):.1%}
   Keywords: {', '.join(catalyst.get('keywords', []))}
"""
        
        prompt += """
Please provide a comprehensive analysis addressing:

1. SPECIFIC NEWS CATALYSTS:
   - Which news events are most likely driving current stock movement?
   - How do these catalysts specifically impact the CSP position?
   - What is the timeline and magnitude of expected impact?

2. TECHNICAL ANALYSIS INTEGRATION:
   - How do current price levels relate to the CSP strike price?
   - What are the key support/resistance levels relevant to this position?
   - How does current volatility affect the position?

3. OPTIONS-SPECIFIC IMPACT:
   - How are the Greeks (delta, theta, gamma, vega) being affected?
   - What is the impact on implied volatility and time decay?
   - How has the probability of assignment changed?

4. STRATEGIC RECOMMENDATIONS:
   - Should the position be held, rolled, or closed?
   - What are the specific action items and timing?
   - How does this align with Erica's CSP management rules?

5. RISK ASSESSMENT:
   - What are the probability scenarios for different outcomes?
   - What are the key risk factors to monitor?
   - What is the maximum loss potential if assigned?

Provide specific, actionable insights with exact price levels, dates, and percentages where applicable.
"""
        
        return prompt
    
    @staticmethod
    def get_position_management_prompt(symbol: str, analysis_data: Dict[str, Any]) -> str:
        """Generate prompt for position management analysis"""
        
        csp_data = analysis_data.get('comprehensive_csp_analysis', {}).get(symbol, {})
        
        prompt = f"""
CASH SECURED PUT POSITION MANAGEMENT ANALYSIS

Current Analysis for {symbol}:
- Position Status: {csp_data.get('position_status', 'N/A')}
- Risk Assessment: {csp_data.get('risk_assessment', 'N/A')}
- Strategic Recommendation: {csp_data.get('strategic_recommendation', 'N/A')}
- Erica's Recommendation: {csp_data.get('erica_recommendation', 'N/A')}
- Management Guidance: {csp_data.get('management_guidance', 'N/A')}

Action Items:
"""
        
        action_items = csp_data.get('action_items', [])
        for i, item in enumerate(action_items, 1):
            prompt += f"{i}. {item}\n"
        
        prompt += """
Probability Scenarios:
"""
        
        scenarios = csp_data.get('probability_scenarios', {})
        for scenario, probability in scenarios.items():
            prompt += f"- {scenario.replace('_', ' ').title()}: {probability:.1%}\n"
        
        prompt += """
Please provide expert guidance on:

1. IMMEDIATE ACTIONS:
   - What should be done today regarding this position?
   - Are there any time-sensitive decisions required?
   - What alerts or monitoring should be set up?

2. ERICA'S METHODOLOGY COMPLIANCE:
   - How well does this position align with Erica's CSP rules?
   - What adjustments would Erica recommend?
   - Are we following proper profit-taking and risk management?

3. MARKET CONTEXT:
   - How do current market conditions affect this position?
   - What macro factors should influence the decision?
   - How does sector performance impact the outlook?

4. SCENARIO PLANNING:
   - What happens if the stock continues current trend?
   - What if there's a sudden reversal?
   - How should we prepare for different outcomes?

5. PORTFOLIO INTEGRATION:
   - How does this position fit within overall portfolio risk?
   - Are there correlation risks with other positions?
   - What is the optimal position sizing going forward?

Focus on actionable, specific recommendations with clear reasoning based on current market data.
"""
        
        return prompt
    
    @staticmethod
    def get_technical_analysis_prompt(symbol: str, analysis_data: Dict[str, Any]) -> str:
        """Generate prompt for technical analysis of CSP position"""
        
        technical_data = analysis_data.get('technical_analysis', {}).get(symbol, {})
        csp_data = analysis_data.get('comprehensive_csp_analysis', {}).get(symbol, {})
        
        prompt = f"""
TECHNICAL ANALYSIS FOR CASH SECURED PUT POSITION

{symbol} Technical Levels:
- Current Price: ${technical_data.get('current_price', 'N/A')}
- Support Levels: {technical_data.get('support_levels', [])}
- Resistance Levels: {technical_data.get('resistance_levels', [])}
- Trend Direction: {technical_data.get('trend_direction', 'N/A')}
- Volatility Regime: {technical_data.get('volatility_regime', 'N/A')}

CSP Position Context:
- Strike Price: ${csp_data.get('position_details', {}).get('strike_price', 'N/A')}
- Distance to Strike: {((technical_data.get('current_price', 0) - csp_data.get('position_details', {}).get('strike_price', 0)) / technical_data.get('current_price', 1) * 100):.1f}%

Please analyze:

1. STRIKE PRICE ANALYSIS:
   - How does the CSP strike relate to key technical levels?
   - Is the strike above/below major support?
   - What is the technical probability of reaching the strike?

2. TREND ANALYSIS:
   - What is the current trend strength and direction?
   - Are there signs of trend continuation or reversal?
   - How does this affect assignment probability?

3. VOLATILITY IMPACT:
   - How does current volatility compare to historical levels?
   - What does this mean for option premium and time decay?
   - Should we expect volatility expansion or contraction?

4. KEY LEVELS TO WATCH:
   - What price levels should trigger management actions?
   - Where are the critical support/resistance zones?
   - What would invalidate the current technical setup?

5. TIMING CONSIDERATIONS:
   - Based on technical patterns, what is the optimal exit timing?
   - Are there upcoming technical events (breakouts, etc.)?
   - How does time decay interact with technical levels?

Provide specific price targets and technical reasoning for position management decisions.
"""
        
        return prompt
    
    @staticmethod
    def detect_csp_question_type(question: str) -> str:
        """Detect the type of CSP-related question"""
        question_lower = question.lower()
        
        # News catalyst questions
        if any(word in question_lower for word in ['news', 'catalyst', 'why', 'reason', 'cause', 'driving']):
            return 'news_catalyst'
        
        # Position management questions
        elif any(word in question_lower for word in ['manage', 'hold', 'close', 'roll', 'exit', 'action']):
            return 'position_management'
        
        # Technical analysis questions
        elif any(word in question_lower for word in ['technical', 'support', 'resistance', 'chart', 'level']):
            return 'technical_analysis'
        
        # Risk assessment questions
        elif any(word in question_lower for word in ['risk', 'probability', 'assignment', 'loss']):
            return 'risk_assessment'
        
        # General CSP questions
        elif any(word in question_lower for word in ['csp', 'cash secured put', 'put']):
            return 'general_csp'
        
        else:
            return 'general'
    
    @staticmethod
    def enhance_user_question(question: str, symbol: str, analysis_data: Dict[str, Any]) -> str:
        """Enhance user question with relevant context"""
        
        question_type = CSPAnalysisPrompts.detect_csp_question_type(question)
        
        enhanced_question = f"USER QUESTION: {question}\n\n"
        
        if question_type == 'news_catalyst':
            enhanced_question += CSPAnalysisPrompts.get_news_catalyst_prompt(symbol, analysis_data)
        elif question_type == 'position_management':
            enhanced_question += CSPAnalysisPrompts.get_position_management_prompt(symbol, analysis_data)
        elif question_type == 'technical_analysis':
            enhanced_question += CSPAnalysisPrompts.get_technical_analysis_prompt(symbol, analysis_data)
        else:
            # For general questions, provide comprehensive context
            enhanced_question += f"""
COMPREHENSIVE CONTEXT FOR {symbol}:

Please provide expert-level analysis addressing the user's question with specific reference to:
- Current market data and real-time conditions
- Cash Secured Put position details and Greeks
- Recent news catalysts and their impact
- Technical analysis and key price levels
- Risk assessment and probability scenarios
- Strategic recommendations aligned with Erica's methodology
- Specific action items with timing and price targets

Use the provided real-time market context to give precise, actionable insights.
"""
        
        return enhanced_question
