#!/usr/bin/env python3
"""
Direct GUI launcher for the Erica trading system
This script launches the GUI directly without shell dependencies
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    """Launch the GUI directly"""
    print("🚀 Launching Erica Trading System GUI...")
    
    try:
        # Import and run the desktop app
        from desktop_app import TradingSystemGUI
        import tkinter as tk
        
        print("✅ Modules imported successfully")
        
        # Create and run the application
        app = TradingSystemGUI()
        print("✅ GUI application created")
        print("🎯 Starting main event loop...")
        
        # Start the GUI
        app.root.mainloop()
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Please ensure all required modules are installed")
        return False
        
    except Exception as e:
        print(f"❌ Error launching GUI: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print("✅ GUI application closed successfully")
    else:
        print("❌ GUI application failed to start")
        sys.exit(1)
