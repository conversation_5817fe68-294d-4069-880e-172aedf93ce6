"""
Comprehensive Trading Assistant for Erica's Trading System

Provides intelligent responses to market questions, strategy education,
and comprehensive trading guidance using all system components.
"""

import tkinter as tk
from tkinter import ttk, scrolledtext
from typing import Dict, List, Optional, Any
import threading
import re

from strategy_knowledge_base import StrategyKnowledgeBase
from market_intelligence import MarketIntelligenceEngine
from enhanced_strategy_engine import EnhancedStrategyEngine
from intelligent_strategy_engine import IntelligentStrategyEngine

class TradingAssistant:
    """Comprehensive trading assistant with market intelligence and education"""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.strategy_kb = StrategyKnowledgeBase()
        self.intelligence_engine = MarketIntelligenceEngine(api_key)
        self.enhanced_engine = EnhancedStrategyEngine(api_key)
        self.base_engine = IntelligentStrategyEngine(api_key)
        
        # Conversation history
        self.conversation_history = []
    
    def answer_question(self, question: str) -> str:
        """Answer any trading or market-related question"""
        
        question_lower = question.lower()
        
        # Strategy-specific questions
        if self._is_strategy_question(question_lower):
            return self._answer_strategy_question(question)
        
        # Market condition questions
        elif self._is_market_question(question_lower):
            return self._answer_market_question(question)
        
        # Educational questions
        elif self._is_educational_question(question_lower):
            return self._answer_educational_question(question)
        
        # Symbol-specific questions
        elif self._is_symbol_question(question_lower):
            return self._answer_symbol_question(question)
        
        # Risk management questions
        elif self._is_risk_question(question_lower):
            return self._answer_risk_question(question)
        
        # General trading questions
        else:
            return self._answer_general_question(question)
    
    def _is_strategy_question(self, question: str) -> bool:
        """Check if question is about trading strategies"""
        strategy_keywords = [
            'strategy', 'covered call', 'credit spread', 'leaps', 'wheel', 
            'premium selling', 'best strategy', 'recommend', 'trade'
        ]
        return any(keyword in question for keyword in strategy_keywords)
    
    def _is_market_question(self, question: str) -> bool:
        """Check if question is about market conditions"""
        market_keywords = [
            'market', 'trend', 'direction', 'volatility', 'vix', 'bullish', 
            'bearish', 'sector', 'rotation', 'environment'
        ]
        return any(keyword in question for keyword in market_keywords)
    
    def _is_educational_question(self, question: str) -> bool:
        """Check if question is educational"""
        educational_keywords = [
            'what is', 'how does', 'explain', 'define', 'delta', 'theta', 
            'gamma', 'vega', 'iv rank', 'greeks', 'learn', 'understand'
        ]
        return any(keyword in question for keyword in educational_keywords)
    
    def _is_symbol_question(self, question: str) -> bool:
        """Check if question is about specific symbols"""
        symbols = ['amd', 'nvda', 'googl', 'aapl', 'amzn', 'spy', 'qqq']
        return any(symbol in question for symbol in symbols)
    
    def _is_risk_question(self, question: str) -> bool:
        """Check if question is about risk management"""
        risk_keywords = [
            'risk', 'position size', 'stop loss', 'risk management', 
            'how much', 'safe', 'dangerous', 'loss'
        ]
        return any(keyword in question for keyword in risk_keywords)
    
    def _answer_strategy_question(self, question: str) -> str:
        """Answer strategy-related questions"""
        
        question_lower = question.lower()
        
        # Best strategy questions
        if 'best strategy' in question_lower or 'recommend' in question_lower:
            try:
                intelligence = self.intelligence_engine.get_market_intelligence()
                
                response = f"Based on current market conditions:\n\n"
                response += f"**Market Environment:** {intelligence.trend_direction.value.replace('_', ' ').title()} trend "
                response += f"in {intelligence.volatility_regime.value.replace('_', ' ')} volatility environment\n\n"
                response += f"**Best Strategies Today:**\n"
                
                for i, strategy_type in enumerate(intelligence.best_strategy_types, 1):
                    strategy_profile = self.strategy_kb.strategies.get(strategy_type)
                    if strategy_profile:
                        response += f"{i}. **{strategy_profile.name}** - {strategy_profile.description}\n"
                
                response += f"\n**Rationale:** {intelligence.strategy_rationale}\n"
                response += f"**Risk Level:** {intelligence.risk_level}"
                
                return response
                
            except Exception as e:
                return "I'm having trouble accessing current market data. Please try again in a moment."
        
        # Specific strategy questions
        for strategy_name, strategy_profile in self.strategy_kb.strategies.items():
            if strategy_name.replace('_', ' ') in question_lower or strategy_profile.name.lower() in question_lower:
                return self._explain_strategy(strategy_profile)
        
        return "I can help you with covered calls, credit spreads, LEAPS, premium selling, and the wheel strategy. What would you like to know?"
    
    def _answer_market_question(self, question: str) -> str:
        """Answer market condition questions"""
        
        try:
            return self.intelligence_engine.answer_market_question(question)
        except Exception as e:
            return "I'm having trouble accessing current market data. Please try again in a moment."
    
    def _answer_educational_question(self, question: str) -> str:
        """Answer educational questions about trading concepts"""
        
        question_lower = question.lower()
        
        # Greeks questions
        if 'delta' in question_lower:
            return self._explain_concept('delta')
        elif 'theta' in question_lower:
            return self._explain_concept('theta')
        elif 'gamma' in question_lower:
            return self._explain_concept('gamma')
        elif 'vega' in question_lower:
            return self._explain_concept('vega')
        elif 'iv rank' in question_lower or 'implied volatility' in question_lower:
            return self._explain_concept('iv_rank')
        
        # General concepts
        elif 'option' in question_lower:
            return self._explain_options_basics()
        elif 'risk management' in question_lower:
            return self._explain_risk_management()
        elif 'position sizing' in question_lower:
            return self._explain_position_sizing()
        
        return "I can explain trading concepts like the Greeks (delta, theta, gamma, vega), IV rank, options basics, risk management, and position sizing. What would you like to learn about?"
    
    def _answer_symbol_question(self, question: str) -> str:
        """Answer questions about specific symbols"""
        
        # Extract symbol from question
        symbols = ['AMD', 'NVDA', 'GOOGL', 'AAPL', 'AMZN']
        question_upper = question.upper()
        
        symbol = None
        for sym in symbols:
            if sym in question_upper:
                symbol = sym
                break
        
        if not symbol:
            return "I can provide analysis for AMD, NVDA, GOOGL, AAPL, and AMZN. Which symbol would you like to know about?"
        
        try:
            enhanced_rec = self.enhanced_engine.get_enhanced_recommendation(symbol)
            
            response = f"**{symbol} Analysis:**\n\n"
            response += f"**Recommended Strategy:** {enhanced_rec.primary_strategy.value.replace('_', ' ').title()}\n"
            response += f"**Confidence:** {enhanced_rec.confidence:.0%}\n\n"
            response += f"**Why This Strategy:** {enhanced_rec.why_this_strategy}\n\n"
            response += f"**Market Context:** {enhanced_rec.market_context}\n\n"
            response += f"**Risk Assessment:** {enhanced_rec.risk_assessment}\n\n"
            response += f"**Position Sizing:** {enhanced_rec.position_sizing_guidance}"
            
            return response
            
        except Exception as e:
            return f"I'm having trouble analyzing {symbol} right now. Please try again in a moment."
    
    def _answer_risk_question(self, question: str) -> str:
        """Answer risk management questions"""
        
        question_lower = question.lower()
        
        if 'position size' in question_lower or 'how much' in question_lower:
            return self._explain_position_sizing()
        elif 'stop loss' in question_lower:
            return self._explain_stop_losses()
        elif 'risk management' in question_lower:
            return self._explain_risk_management()
        
        return "Risk management is crucial in trading. I can explain position sizing, stop losses, and general risk management principles. What specific aspect would you like to know about?"
    
    def _answer_general_question(self, question: str) -> str:
        """Answer general trading questions"""
        
        try:
            intelligence = self.intelligence_engine.get_market_intelligence()
            
            response = "Here's a general market overview:\n\n"
            response += f"**Current Market:** {intelligence.trend_direction.value.replace('_', ' ').title()} trend\n"
            response += f"**Volatility:** {intelligence.volatility_regime.value.replace('_', ' ').title()} (VIX: {intelligence.vix_level:.1f})\n"
            response += f"**Best Strategies:** {', '.join(intelligence.best_strategy_types)}\n"
            response += f"**Risk Level:** {intelligence.risk_level}\n\n"
            response += "I can help with specific strategies, market analysis, educational content, or symbol-specific recommendations. What would you like to know?"
            
            return response
            
        except Exception as e:
            return "I'm your comprehensive trading assistant. I can help with strategy recommendations, market analysis, educational content, and specific symbol analysis. What would you like to know?"
    
    def _explain_strategy(self, strategy_profile) -> str:
        """Explain a specific strategy in detail"""
        
        explanation = f"**{strategy_profile.name}**\n\n"
        explanation += f"**Description:** {strategy_profile.description}\n\n"
        explanation += f"**How It Works:** {strategy_profile.how_it_works}\n\n"
        explanation += f"**When to Use:**\n"
        for condition in strategy_profile.when_to_use:
            explanation += f"• {condition}\n"
        
        explanation += f"\n**Profit/Loss Profile:** {strategy_profile.profit_loss_profile}\n\n"
        
        explanation += f"**Greeks Exposure:**\n"
        for greek, exposure in strategy_profile.greeks_exposure.items():
            explanation += f"• **{greek.title()}:** {exposure}\n"
        
        explanation += f"\n**Success Tips:**\n"
        for tip in strategy_profile.success_tips:
            explanation += f"• {tip}\n"
        
        explanation += f"\n**Common Mistakes to Avoid:**\n"
        for mistake in strategy_profile.common_mistakes:
            explanation += f"• {mistake}\n"
        
        return explanation
    
    def _explain_concept(self, concept: str) -> str:
        """Explain trading concepts"""
        
        concept_info = self.strategy_kb.market_concepts.get(concept)
        if concept_info:
            explanation = f"**{concept_info.name}**\n\n"
            explanation += f"**Definition:** {concept_info.definition}\n\n"
            explanation += f"**Importance:** {concept_info.importance}\n\n"
            explanation += f"**How to Use:** {concept_info.how_to_use}\n\n"
            
            if concept_info.examples:
                explanation += f"**Examples:**\n"
                for example in concept_info.examples:
                    explanation += f"• {example}\n"
            
            return explanation
        
        # Fallback explanations for concepts not in knowledge base
        if concept == 'theta':
            return "**Theta (Time Decay)**\n\nTheta measures how much an option's price decreases as time passes. It's always negative for long options and positive for short options. Time decay accelerates as expiration approaches, especially in the final 30 days."
        
        elif concept == 'gamma':
            return "**Gamma**\n\nGamma measures the rate of change in delta. High gamma means delta changes quickly as the stock moves. Gamma is highest for at-the-money options near expiration."
        
        elif concept == 'vega':
            return "**Vega**\n\nVega measures sensitivity to changes in implied volatility. Long options have positive vega (benefit from volatility increases), while short options have negative vega (benefit from volatility decreases)."
        
        return "I can explain various trading concepts. Please ask about specific topics like delta, theta, gamma, vega, IV rank, or other trading terms."
    
    def _explain_options_basics(self) -> str:
        """Explain options basics"""
        
        return """**Options Basics**

Options are contracts that give you the right (but not obligation) to buy or sell a stock at a specific price by a certain date.

**Key Components:**
• **Strike Price:** The price at which you can buy/sell
• **Expiration Date:** When the option expires
• **Premium:** The cost to buy the option

**Types:**
• **Calls:** Right to buy (bullish)
• **Puts:** Right to sell (bearish)

**Strategies:**
• **Buy options:** Pay premium, limited risk, unlimited potential
• **Sell options:** Collect premium, higher probability, limited profit

**The Greeks:** Delta, theta, gamma, vega measure different risks and sensitivities."""
    
    def _explain_risk_management(self) -> str:
        """Explain risk management principles"""
        
        return """**Risk Management Principles**

**Position Sizing:**
• Never risk more than 2% of your account on a single trade
• Limit exposure to any single stock to 25% of portfolio

**Profit Taking:**
• Take profits at 50-70% of maximum gain for credit strategies
• Take profits at 30-50% for debit strategies

**Loss Management:**
• Set stop losses at 2x credit received for credit spreads
• Close positions at 21 DTE to avoid gamma risk
• Roll positions when thesis intact but position challenged

**Portfolio Management:**
• Diversify across strategies and underlyings
• Monitor total portfolio risk daily
• Adjust position sizes based on market volatility"""
    
    def _explain_position_sizing(self) -> str:
        """Explain position sizing guidelines"""
        
        return """**Position Sizing Guidelines**

**Basic Rule:** Risk no more than 2% of your account per trade

**Calculation Example:**
• $100,000 account = $2,000 max risk per trade
• Credit spread with $500 max loss = 4 contracts max
• Covered call on $50,000 in stock = appropriate size

**Market Adjustments:**
• **High volatility:** Reduce to 1% risk per trade
• **Low volatility:** Can increase to 3% on best setups
• **Earnings season:** Reduce size by 50%

**Concentration Limits:**
• Maximum 25% of portfolio in any single underlying
• Maximum 6 open positions at once for beginners
• Scale up gradually as you gain experience"""
    
    def _explain_stop_losses(self) -> str:
        """Explain stop loss strategies"""
        
        return """**Stop Loss Strategies**

**Credit Strategies:**
• Stop loss at 2x credit received
• Example: Received $1.00 credit, stop at $2.00 loss

**Debit Strategies:**
• Stop loss at 30-50% of premium paid
• Example: Paid $5.00, stop at $2.50-$1.50 loss

**Time-Based Stops:**
• Close all positions at 21 DTE
• Avoid gamma risk near expiration

**Technical Stops:**
• Exit if key support/resistance breaks
• Use ATR-based stops for volatility adjustment

**Thesis Stops:**
• Exit immediately if original thesis changes
• Don't hope for recovery if fundamentals shift"""

class TradingAssistantWidget(ttk.Frame):
    """GUI widget for the trading assistant"""
    
    def __init__(self, parent, api_key: str, **kwargs):
        super().__init__(parent, **kwargs)
        
        self.assistant = TradingAssistant(api_key)
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the trading assistant user interface"""
        
        # Header
        header_frame = ttk.Frame(self)
        header_frame.pack(fill=tk.X, padx=10, pady=(10, 5))
        
        title_label = ttk.Label(header_frame, text="🤖 Erica's Trading Assistant", 
                               font=('Arial', 16, 'bold'))
        title_label.pack(side=tk.LEFT)
        
        # Chat area
        chat_frame = ttk.LabelFrame(self, text="Conversation")
        chat_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        self.chat_display = scrolledtext.ScrolledText(chat_frame, height=20, 
                                                     font=('Arial', 10), wrap=tk.WORD)
        self.chat_display.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Input area
        input_frame = ttk.Frame(self)
        input_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(input_frame, text="Ask me anything about trading:").pack(anchor=tk.W)
        
        entry_frame = ttk.Frame(input_frame)
        entry_frame.pack(fill=tk.X, pady=(5, 0))
        
        self.question_entry = ttk.Entry(entry_frame, font=('Arial', 11))
        self.question_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))
        self.question_entry.bind('<Return>', self._on_ask_question)
        
        ask_button = ttk.Button(entry_frame, text="Ask", command=self._on_ask_question)
        ask_button.pack(side=tk.RIGHT)
        
        # Quick questions
        quick_frame = ttk.LabelFrame(self, text="Quick Questions")
        quick_frame.pack(fill=tk.X, padx=10, pady=5)
        
        quick_buttons_frame = ttk.Frame(quick_frame)
        quick_buttons_frame.pack(fill=tk.X, padx=5, pady=5)
        
        quick_questions = [
            "What's the best strategy for today?",
            "Explain covered calls",
            "What is IV rank?",
            "How should I size positions?",
            "What's the market trend?"
        ]
        
        for i, question in enumerate(quick_questions):
            btn = ttk.Button(quick_buttons_frame, text=question, 
                           command=lambda q=question: self._ask_quick_question(q))
            btn.grid(row=i//3, column=i%3, padx=2, pady=2, sticky='ew')
        
        # Configure grid weights
        for i in range(3):
            quick_buttons_frame.columnconfigure(i, weight=1)
        
        # Welcome message
        self._add_message("Assistant", "Hello! I'm your comprehensive trading assistant. I can help with:\n\n• Strategy recommendations based on current market conditions\n• Educational content about options and trading concepts\n• Symbol-specific analysis and recommendations\n• Risk management and position sizing guidance\n• Market analysis and trend insights\n\nWhat would you like to know?")
    
    def _on_ask_question(self, event=None):
        """Handle ask question button/enter key"""
        question = self.question_entry.get().strip()
        if question:
            self._ask_question(question)
            self.question_entry.delete(0, tk.END)
    
    def _ask_quick_question(self, question: str):
        """Ask a predefined quick question"""
        self._ask_question(question)
    
    def _ask_question(self, question: str):
        """Ask a question and get response"""
        
        # Add user question to chat
        self._add_message("You", question)
        
        # Show thinking message
        thinking_msg = "Thinking..."
        self._add_message("Assistant", thinking_msg)
        
        def get_response():
            try:
                response = self.assistant.answer_question(question)
                # Remove thinking message and add real response
                self.after(0, lambda: self._replace_last_message(response))
            except Exception as e:
                error_response = "I'm sorry, I encountered an error processing your question. Please try again."
                self.after(0, lambda: self._replace_last_message(error_response))
        
        # Get response in background thread
        threading.Thread(target=get_response, daemon=True).start()
    
    def _add_message(self, sender: str, message: str):
        """Add a message to the chat display"""
        
        self.chat_display.insert(tk.END, f"\n{sender}: {message}\n")
        self.chat_display.insert(tk.END, "-" * 50 + "\n")
        self.chat_display.see(tk.END)
    
    def _replace_last_message(self, new_message: str):
        """Replace the last assistant message with new content"""
        
        # Get all content
        content = self.chat_display.get(1.0, tk.END)
        
        # Find last "Assistant:" message
        lines = content.split('\n')
        for i in range(len(lines) - 1, -1, -1):
            if lines[i].startswith('Assistant:'):
                # Replace from this line to end
                self.chat_display.delete(f"{i+1}.0", tk.END)
                self.chat_display.insert(tk.END, f"Assistant: {new_message}\n")
                self.chat_display.insert(tk.END, "-" * 50 + "\n")
                self.chat_display.see(tk.END)
                break
