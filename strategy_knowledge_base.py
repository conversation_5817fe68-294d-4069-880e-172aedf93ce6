"""
Comprehensive Strategy Knowledge Base for Erica's Trading System

This module contains all trading strategies, methodologies, risk management rules,
and educational content that powers the intelligent trading assistant.
"""

from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum
import json

class StrategyCategory(Enum):
    INCOME_GENERATION = "income_generation"
    VOLATILITY_CAPTURE = "volatility_capture"
    DIRECTIONAL_PLAYS = "directional_plays"
    RISK_MANAGEMENT = "risk_management"

class MarketRegime(Enum):
    LOW_VOLATILITY = "low_volatility"
    HIGH_VOLATILITY = "high_volatility"
    TRENDING = "trending"
    RANGE_BOUND = "range_bound"
    EARNINGS_SEASON = "earnings_season"

@dataclass
class StrategyProfile:
    """Complete profile of a trading strategy"""
    name: str
    category: StrategyCategory
    description: str
    when_to_use: List[str]
    market_conditions: List[MarketRegime]
    iv_rank_preference: Tuple[float, float]  # (min, max) percentile
    
    # Erica's specific parameters
    erica_parameters: Dict[str, Any]
    risk_management: Dict[str, Any]
    position_sizing: Dict[str, Any]
    
    # Educational content
    how_it_works: str
    profit_loss_profile: str
    greeks_exposure: Dict[str, str]
    common_mistakes: List[str]
    success_tips: List[str]

@dataclass
class MarketConcept:
    """Educational content for market concepts"""
    name: str
    definition: str
    importance: str
    how_to_use: str
    examples: List[str]
    related_concepts: List[str]

class StrategyKnowledgeBase:
    """Comprehensive knowledge base for trading strategies and market concepts"""
    
    def __init__(self):
        self.strategies = self._initialize_strategies()
        self.market_concepts = self._initialize_market_concepts()
        self.risk_management_rules = self._initialize_risk_rules()
        self.position_sizing_guidelines = self._initialize_position_sizing()
        
    def _initialize_strategies(self) -> Dict[str, StrategyProfile]:
        """Initialize all trading strategies with Erica's methodology"""
        
        strategies = {}
        
        # Covered Calls
        strategies["covered_call"] = StrategyProfile(
            name="Covered Call",
            category=StrategyCategory.INCOME_GENERATION,
            description="Sell call options against owned stock to generate income",
            when_to_use=[
                "Neutral to slightly bullish outlook",
                "Moderate IV environment (30-70 percentile)",
                "Want to generate income from stock holdings",
                "Expecting sideways to modest upward movement"
            ],
            market_conditions=[MarketRegime.LOW_VOLATILITY, MarketRegime.RANGE_BOUND],
            iv_rank_preference=(30.0, 70.0),
            erica_parameters={
                "baseline": {"target_delta": [0.20, 0.30], "dte": [7, 21]},
                "fast_money": {"target_delta": [0.25, 0.30], "dte": [0, 3]},
                "earnings": {"target_delta": [0.10, 0.15], "dte": [5, 12], "use_em_guardrail": True},
                "near_earnings": {"target_delta_max": 0.10, "buffer_pct": 0.20}
            },
            risk_management={
                "roll_trigger": "Delta > 0.35 or price within 1 ATR of strike",
                "profit_target": "50-70% of credit received",
                "max_loss": "Unlimited downside on shares",
                "time_management": "Roll out/up for credit if threatened ITM"
            },
            position_sizing={
                "max_per_position": "25% of portfolio per ticker",
                "contracts_per_100_shares": 1,
                "scaling": "Start with 1-2 contracts, scale up with experience"
            },
            how_it_works="Sell call options against stock you own. Collect premium income. If called away, keep premium + capital gains up to strike.",
            profit_loss_profile="Limited profit (premium + capital gains to strike), unlimited downside risk on shares",
            greeks_exposure={
                "delta": "Short delta from call, long delta from shares (net neutral to slightly positive)",
                "theta": "Positive - benefit from time decay",
                "vega": "Negative - benefit from volatility decrease",
                "gamma": "Negative - position moves against you as stock moves"
            },
            common_mistakes=[
                "Selling calls too close to the money",
                "Not rolling when delta breached",
                "Ignoring earnings dates",
                "Over-concentrating in single name"
            ],
            success_tips=[
                "Use technical resistance levels for strike selection",
                "Roll up and out for credits when possible",
                "Take profits at 50-70% of max gain",
                "Avoid earnings unless using conservative strikes"
            ]
        )
        
        # Credit Spreads
        strategies["credit_spread"] = StrategyProfile(
            name="Credit Spread",
            category=StrategyCategory.VOLATILITY_CAPTURE,
            description="Sell higher premium option, buy lower premium option for net credit",
            when_to_use=[
                "High IV environment (>50 percentile)",
                "Neutral to directional bias",
                "Want defined risk/reward",
                "Expecting volatility contraction"
            ],
            market_conditions=[MarketRegime.HIGH_VOLATILITY, MarketRegime.RANGE_BOUND],
            iv_rank_preference=(50.0, 100.0),
            erica_parameters={
                "bear_call_spread": {"short_delta": [0.20, 0.30], "dte": [14, 35], "width": [5, 15]},
                "put_credit_spread": {"short_delta": [0.20, 0.30], "dte": [14, 35], "width": [5, 15]},
                "profit_targets": {"take_profit_pct": [0.5, 0.7]}
            },
            risk_management={
                "max_loss": "Width of spread minus credit received",
                "profit_target": "50-70% of credit received",
                "roll_conditions": "Roll out/adjust if thesis intact",
                "stop_loss": "2x credit received or 21 DTE"
            },
            position_sizing={
                "max_risk_per_trade": "2% of account",
                "position_concentration": "Max 25% in single underlying",
                "scaling": "Start small, increase size with win rate"
            },
            how_it_works="Sell option with higher premium, buy option with lower premium. Profit from time decay and volatility contraction.",
            profit_loss_profile="Limited profit (credit received), limited loss (spread width - credit)",
            greeks_exposure={
                "delta": "Directional exposure based on spread type",
                "theta": "Positive - benefit from time decay",
                "vega": "Negative - benefit from volatility decrease",
                "gamma": "Negative near short strike, positive near long strike"
            },
            common_mistakes=[
                "Trading in low IV environment",
                "Not taking profits at 50-70%",
                "Holding too close to expiration",
                "Ignoring technical levels for strike selection"
            ],
            success_tips=[
                "Trade when IV rank > 50",
                "Use support/resistance for strike selection",
                "Take profits early and often",
                "Roll out if thesis remains intact"
            ]
        )

        # LEAPS (Long-term Equity Anticipation Securities)
        strategies["leaps"] = StrategyProfile(
            name="LEAPS",
            category=StrategyCategory.DIRECTIONAL_PLAYS,
            description="Long-term call options (9+ months) for leveraged equity exposure",
            when_to_use=[
                "Strong bullish long-term outlook",
                "Want leveraged exposure with defined risk",
                "Low to moderate IV environment",
                "Substitute for stock ownership"
            ],
            market_conditions=[MarketRegime.LOW_VOLATILITY, MarketRegime.TRENDING],
            iv_rank_preference=(0.0, 50.0),
            erica_parameters={
                "delta_range": [0.60, 0.75],
                "tenor_days": [270, 730],
                "strike_selection": "ITM for stability",
                "management": "Sell short calls against for income (diagonal)"
            },
            risk_management={
                "max_loss": "Premium paid",
                "profit_target": "No fixed target - trend following",
                "time_management": "Monitor theta decay acceleration",
                "adjustment": "Roll out/up if underlying moves favorably"
            },
            position_sizing={
                "max_risk_per_trade": "3% of account",
                "leverage_consideration": "Each contract controls 100 shares",
                "scaling": "Start with 1-2 contracts"
            },
            how_it_works="Buy long-term call options for leveraged exposure to stock appreciation with limited downside risk.",
            profit_loss_profile="Unlimited upside potential, limited downside (premium paid)",
            greeks_exposure={
                "delta": "High positive delta (60-75)",
                "theta": "Negative but minimal due to long time frame",
                "vega": "Positive - benefits from volatility increase",
                "gamma": "Moderate positive gamma"
            },
            common_mistakes=[
                "Buying when IV is elevated",
                "Not managing time decay properly",
                "Over-leveraging position size",
                "Ignoring dividend impact"
            ],
            success_tips=[
                "Buy when IV rank < 50",
                "Choose ITM strikes for stability",
                "Consider selling short calls against for income",
                "Monitor quarterly for fundamental changes"
            ]
        )

        # Premium Selling (Cash Secured Puts)
        strategies["premium_selling"] = StrategyProfile(
            name="Premium Selling",
            category=StrategyCategory.INCOME_GENERATION,
            description="Sell cash-secured puts to generate income and potentially acquire stock",
            when_to_use=[
                "Bullish to neutral outlook",
                "High IV environment (>50 percentile)",
                "Want to acquire stock at discount",
                "Generate income while waiting for entry"
            ],
            market_conditions=[MarketRegime.HIGH_VOLATILITY, MarketRegime.RANGE_BOUND],
            iv_rank_preference=(50.0, 100.0),
            erica_parameters={
                "csp_delta": [0.15, 0.25],
                "dte": [14, 35],
                "strike_selection": "At or below support levels",
                "cash_requirement": "100% of assignment value"
            },
            risk_management={
                "assignment_risk": "Be prepared to own stock",
                "profit_target": "50-70% of credit received",
                "roll_management": "Roll down/out if support holds",
                "stop_loss": "2x credit received"
            },
            position_sizing={
                "max_risk_per_trade": "5% of account (if assigned)",
                "cash_allocation": "Keep sufficient cash for assignment",
                "diversification": "Spread across multiple underlyings"
            },
            how_it_works="Sell put options with cash backing. Keep premium if expires worthless, or get assigned stock at strike price.",
            profit_loss_profile="Limited profit (premium), substantial downside if assigned and stock falls",
            greeks_exposure={
                "delta": "Negative delta (short puts)",
                "theta": "Positive - benefit from time decay",
                "vega": "Negative - benefit from volatility decrease",
                "gamma": "Negative - position moves against you"
            },
            common_mistakes=[
                "Selling puts on stocks you don't want to own",
                "Not keeping sufficient cash for assignment",
                "Ignoring support levels",
                "Trading in low IV environment"
            ],
            success_tips=[
                "Only sell puts on stocks you want to own",
                "Use technical support for strike selection",
                "Keep cash ready for assignment",
                "Consider 'the wheel' strategy if assigned"
            ]
        )

        # The Wheel Strategy
        strategies["wheel"] = StrategyProfile(
            name="The Wheel",
            category=StrategyCategory.INCOME_GENERATION,
            description="Systematic approach: sell CSPs, get assigned, sell covered calls, repeat",
            when_to_use=[
                "Want systematic income generation",
                "Comfortable owning quality stocks",
                "High IV environment for premium collection",
                "Long-term income strategy"
            ],
            market_conditions=[MarketRegime.HIGH_VOLATILITY, MarketRegime.RANGE_BOUND],
            iv_rank_preference=(40.0, 100.0),
            erica_parameters={
                "phase_1": "Sell cash-secured puts",
                "phase_2": "If assigned, sell covered calls",
                "phase_3": "If called away, restart with CSPs",
                "strike_selection": "Technical levels and delta targets"
            },
            risk_management={
                "stock_selection": "Only wheel quality stocks",
                "position_sizing": "Limit exposure per underlying",
                "profit_optimization": "Take profits at 50-70%",
                "loss_management": "Roll for credits when possible"
            },
            position_sizing={
                "max_per_underlying": "10% of portfolio",
                "cash_management": "Keep 50% cash for assignments",
                "scaling": "Start with 1-2 underlyings"
            },
            how_it_works="Continuous cycle of selling puts, getting assigned, selling calls, getting called away, repeat.",
            profit_loss_profile="Consistent income generation, risk of holding declining stocks",
            greeks_exposure={
                "delta": "Varies by phase - negative (CSP) or neutral (CC)",
                "theta": "Always positive - constant time decay benefit",
                "vega": "Negative - benefits from volatility contraction",
                "gamma": "Negative exposure throughout cycle"
            },
            common_mistakes=[
                "Wheeling poor quality stocks",
                "Not taking profits early enough",
                "Inadequate cash management",
                "Ignoring market regime changes"
            ],
            success_tips=[
                "Choose high-quality, liquid stocks",
                "Maintain disciplined profit taking",
                "Keep adequate cash reserves",
                "Adjust strategy based on market conditions"
            ]
        )

        return strategies
    
    def _initialize_market_concepts(self) -> Dict[str, MarketConcept]:
        """Initialize market concepts and educational content"""
        
        concepts = {}
        
        concepts["iv_rank"] = MarketConcept(
            name="IV Rank",
            definition="Implied Volatility Rank - where current IV sits relative to its 52-week range",
            importance="Critical for strategy selection - high IV favors selling, low IV favors buying",
            how_to_use="Use >50 for premium selling strategies, <50 for premium buying strategies",
            examples=[
                "IV Rank 80% = current IV is higher than 80% of the past year",
                "IV Rank 20% = current IV is lower than 80% of the past year"
            ],
            related_concepts=["iv_percentile", "volatility_contraction", "premium_selling"]
        )
        
        concepts["delta"] = MarketConcept(
            name="Delta",
            definition="Rate of change in option price relative to $1 move in underlying stock",
            importance="Measures directional exposure and probability of expiring ITM",
            how_to_use="Use for position sizing, strike selection, and risk management",
            examples=[
                "0.30 delta call = ~30% chance of expiring ITM",
                "Short 0.20 delta put = collect premium with 80% probability of success"
            ],
            related_concepts=["gamma", "theta", "probability", "moneyness"]
        )
        
        return concepts
    
    def _initialize_risk_rules(self) -> Dict[str, Any]:
        """Initialize Erica's risk management rules"""
        
        return {
            "position_sizing": {
                "max_risk_per_trade": 0.02,  # 2% of account
                "max_ticker_exposure": 0.25,  # 25% per underlying
                "max_portfolio_risk": 0.06   # 6% total at-risk capital
            },
            "profit_taking": {
                "credit_strategies": [0.5, 0.7],  # 50-70% of max profit
                "debit_strategies": [0.3, 0.5],   # 30-50% of max profit
                "time_based": "Close at 21 DTE or less"
            },
            "loss_management": {
                "stop_loss_multiplier": 2.0,  # 2x credit received
                "delta_breach": 0.35,          # Roll when short delta > 35
                "time_stop": 21                # Close/roll at 21 DTE
            }
        }
    
    def get_strategy_recommendation(self, market_conditions: Dict[str, Any]) -> Dict[str, Any]:
        """Get strategy recommendation based on current market conditions"""
        
        iv_rank = market_conditions.get('iv_rank', 50)
        trend = market_conditions.get('trend', 'neutral')
        earnings_days = market_conditions.get('earnings_days', 30)
        
        recommendations = []
        
        for strategy_name, strategy in self.strategies.items():
            score = self._calculate_strategy_score(strategy, market_conditions)
            if score > 0.5:  # Threshold for recommendation
                recommendations.append({
                    'strategy': strategy_name,
                    'score': score,
                    'rationale': self._generate_rationale(strategy, market_conditions)
                })
        
        # Sort by score
        recommendations.sort(key=lambda x: x['score'], reverse=True)
        
        return {
            'primary_recommendation': recommendations[0] if recommendations else None,
            'alternatives': recommendations[1:3],
            'market_assessment': self._assess_market_conditions(market_conditions)
        }
    
    def _calculate_strategy_score(self, strategy: StrategyProfile, conditions: Dict[str, Any]) -> float:
        """Calculate suitability score for a strategy given market conditions"""
        
        score = 0.5  # Base score
        
        iv_rank = conditions.get('iv_rank', 50)
        
        # IV rank scoring
        if strategy.iv_rank_preference[0] <= iv_rank <= strategy.iv_rank_preference[1]:
            score += 0.3
        else:
            score -= 0.2
        
        # Earnings proximity
        earnings_days = conditions.get('earnings_days', 30)
        if earnings_days <= 10:
            if 'earnings' in strategy.erica_parameters:
                score += 0.2
            else:
                score -= 0.1
        
        return max(0.0, min(1.0, score))
    
    def _generate_rationale(self, strategy: StrategyProfile, conditions: Dict[str, Any]) -> str:
        """Generate human-readable rationale for strategy selection"""
        
        iv_rank = conditions.get('iv_rank', 50)
        trend = conditions.get('trend', 'neutral')
        
        rationale = f"{strategy.name} selected because: "
        
        if strategy.category == StrategyCategory.VOLATILITY_CAPTURE and iv_rank > 50:
            rationale += f"High IV rank ({iv_rank}%) favors premium selling strategies. "
        
        if strategy.category == StrategyCategory.INCOME_GENERATION:
            rationale += "Suitable for generating consistent income in current market environment. "
        
        return rationale
    
    def _assess_market_conditions(self, conditions: Dict[str, Any]) -> str:
        """Assess overall market conditions"""
        
        iv_rank = conditions.get('iv_rank', 50)
        trend = conditions.get('trend', 'neutral')
        
        if iv_rank > 70:
            return "High volatility environment - favor premium selling strategies"
        elif iv_rank < 30:
            return "Low volatility environment - consider premium buying strategies"
        else:
            return "Moderate volatility environment - balanced approach recommended"
