"""
Simple Enhanced AI Assistant Demo
Basic demonstration of the enhanced AI assistant functionality

This demo shows core functionality without requiring all dependencies.
"""

import os
import sys
from datetime import datetime, timedelta

def load_api_key():
    """Load FMP API key from file or environment"""
    try:
        with open('fmp.key', 'r') as f:
            return f.read().strip()
    except FileNotFoundError:
        return os.getenv('FMP_API_KEY', 'K63wnAbUDHNPICjzeq3JcEG1vi8Q2oz7')

def test_basic_functionality():
    """Test basic functionality of the enhanced AI assistant"""
    print("🚀 Enhanced Options AI Assistant - Simple Demo")
    print("=" * 60)
    
    try:
        from enhanced_options_ai_assistant import (
            EnhancedOptionsAIAssistant, 
            OptionsPosition, 
            PositionType,
            NewsCatalyst,
            CatalystImpact
        )
        
        # Initialize the assistant
        api_key = load_api_key()
        assistant = EnhancedOptionsAIAssistant(fmp_api_key=api_key)
        
        print("✅ Enhanced AI Assistant initialized successfully")
        
        # Test 1: Create sample position
        print("\n📊 Test 1: Creating sample Cash Secured Put position")
        position = OptionsPosition(
            symbol="AAPL",
            position_type=PositionType.CASH_SECURED_PUT,
            entry_date=datetime.now() - timedelta(days=10),
            expiration_date=datetime.now() + timedelta(days=25),
            strike_price=220.0,
            entry_price=3.50,
            quantity=2,
            current_price=2.80,
            days_to_expiration=25,
            original_strategy_rationale="Test CSP position for demo"
        )
        
        assistant.add_portfolio_position(position)
        print(f"✅ Added CSP position: {position.symbol} ${position.strike_price} strike")
        
        # Test 2: Portfolio summary
        print("\n📊 Test 2: Portfolio Summary")
        summary = assistant.get_portfolio_summary()
        print(summary)
        
        # Test 3: Risk analysis
        print("\n📊 Test 3: Risk Analysis")
        risk_analysis = assistant.analyze_portfolio_risk()
        print(f"Total Positions: {risk_analysis['total_positions']}")
        print(f"Symbols: {risk_analysis['symbols_count']}")
        print(f"Position Types: {risk_analysis['position_types']}")
        
        # Test 4: News catalyst classification
        print("\n📊 Test 4: News Catalyst Classification")
        test_headlines = [
            ("Apple beats earnings expectations", "Strong quarterly results"),
            ("Morgan Stanley upgrades AAPL", "Price target raised to $250"),
            ("Apple announces new product", "Minor product update")
        ]
        
        for headline, text in test_headlines:
            catalyst_type, impact = assistant._classify_news_catalyst(headline, text)
            print(f"'{headline}' -> {catalyst_type} ({impact.value})")
        
        # Test 5: Sentiment analysis
        print("\n📊 Test 5: Sentiment Analysis")
        test_sentiments = [
            ("Strong earnings beat", "Excellent growth and positive outlook"),
            ("Earnings miss disappointing", "Weak performance and negative outlook"),
            ("Company announces update", "Regular business update")
        ]
        
        for headline, text in test_sentiments:
            score = assistant._calculate_sentiment_score(headline, text)
            sentiment = "Bullish" if score > 0.1 else "Bearish" if score < -0.1 else "Neutral"
            print(f"'{headline}' -> {score:.2f} ({sentiment})")
        
        # Test 6: IV environment assessment
        print("\n📊 Test 6: IV Environment Assessment")
        iv_levels = [0.85, 0.45, 0.15]
        for iv_rank in iv_levels:
            assessment = assistant._assess_iv_environment(iv_rank, 0.0)
            print(f"IV Rank {iv_rank:.0%}: {assessment}")
        
        # Test 7: Assignment risk assessment
        print("\n📊 Test 7: Assignment Risk Assessment")
        from enhanced_options_ai_assistant import OptionsAnalysisContext
        
        # Create mock context
        class MockContext:
            def __init__(self, current_price):
                self.current_price = current_price
        
        test_prices = [225.0, 222.0, 218.0]  # Strike is 220.0
        for price in test_prices:
            context = MockContext(price)
            risk = assistant._assess_assignment_risk(position, context)
            print(f"Current Price ${price}: {risk} assignment risk")
        
        print("\n✅ All basic functionality tests completed successfully!")
        
        # Test 8: Try comprehensive analysis (may fail due to API limits)
        print("\n📊 Test 8: Attempting comprehensive analysis...")
        try:
            analysis = assistant.analyze_cash_secured_put_catalyst("AAPL")
            print("✅ Comprehensive analysis completed")
            print(f"Analysis length: {len(analysis)} characters")
            # Show first few lines
            lines = analysis.split('\n')[:10]
            for line in lines:
                print(line)
            if len(analysis.split('\n')) > 10:
                print("... (truncated)")
        except Exception as e:
            print(f"⚠️  Comprehensive analysis failed (expected in demo): {e}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Please ensure all required modules are available.")
        return False
    except Exception as e:
        print(f"❌ Error during demo: {e}")
        return False

def test_integration_features():
    """Test integration features"""
    print("\n" + "=" * 60)
    print("🔧 Testing Integration Features")
    print("=" * 60)
    
    try:
        # Test GUI integration components
        print("📱 Testing GUI Integration...")
        
        # This would normally require tkinter and full GUI setup
        # For demo, we'll just test the imports
        try:
            from enhanced_ai_integration import EnhancedAIIntegrationGUI
            print("✅ GUI integration module available")
        except ImportError as e:
            print(f"⚠️  GUI integration not available: {e}")
        
        # Test documentation and guide
        print("\n📚 Checking documentation...")
        if os.path.exists('ENHANCED_AI_ASSISTANT_GUIDE.md'):
            print("✅ User guide available")
        else:
            print("⚠️  User guide not found")
        
        # Test demo scripts
        print("\n🎮 Checking demo scripts...")
        demo_files = [
            'enhanced_options_ai_demo.py',
            'test_enhanced_ai_assistant.py',
            'simple_ai_demo.py'
        ]
        
        for demo_file in demo_files:
            if os.path.exists(demo_file):
                print(f"✅ {demo_file} available")
            else:
                print(f"⚠️  {demo_file} not found")
        
        return True
        
    except Exception as e:
        print(f"❌ Integration test error: {e}")
        return False

def main():
    """Run the simple demo"""
    print("Starting Enhanced Options AI Assistant Simple Demo...")
    
    # Test basic functionality
    basic_success = test_basic_functionality()
    
    # Test integration features
    integration_success = test_integration_features()
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 DEMO SUMMARY")
    print("=" * 60)
    
    if basic_success:
        print("✅ Basic functionality: PASSED")
    else:
        print("❌ Basic functionality: FAILED")
    
    if integration_success:
        print("✅ Integration features: PASSED")
    else:
        print("❌ Integration features: FAILED")
    
    if basic_success and integration_success:
        print("\n🎉 Enhanced Options AI Assistant is ready for use!")
        print("\nNext steps:")
        print("1. Run 'python enhanced_options_ai_demo.py' for full demo")
        print("2. Integrate with your trading dashboard")
        print("3. Add real portfolio positions")
        print("4. Ask questions like 'What is the news catalyst for this Cash Secured Put?'")
    else:
        print("\n⚠️  Some issues detected. Please check the error messages above.")
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    main()
