"""
Enhanced AI Integration Module
Integrates the Enhanced Options AI Assistant with the existing desktop application

This module provides:
1. GUI integration for the enhanced AI assistant
2. Context-aware responses based on current dashboard state
3. Position tracking integration with the trading system
4. Real-time analysis updates
5. Expert-level options trading chat interface

Features:
- Seamless integration with existing desktop app
- Context-aware AI responses
- Real-time market data integration
- Position-specific analysis
- Expert options trading knowledge
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading
import queue
from typing import Dict, List, Optional, Any
from datetime import datetime
import json

from enhanced_options_ai_assistant import (
    EnhancedOptionsAIAssistant, 
    OptionsPosition, 
    PositionType,
    OptionsAnalysisContext
)

class EnhancedAIIntegrationGUI:
    """Enhanced AI Assistant GUI integrated with the desktop application"""
    
    def __init__(self, parent_frame, fmp_api_key: str, openai_api_key: Optional[str] = None):
        self.parent_frame = parent_frame
        self.fmp_api_key = fmp_api_key
        self.openai_api_key = openai_api_key
        
        # Initialize the enhanced AI assistant
        self.ai_assistant = EnhancedOptionsAIAssistant(fmp_api_key, openai_api_key)
        
        # GUI components
        self.chat_frame = None
        self.chat_display = None
        self.input_entry = None
        self.send_button = None
        self.context_label = None
        
        # State management
        self.current_symbol = None
        self.current_dashboard_data = {}
        self.chat_history = []
        
        # Threading for async operations
        self.response_queue = queue.Queue()
        
        self.setup_gui()
        self.start_response_processor()
    
    def setup_gui(self):
        """Setup the enhanced AI assistant GUI"""
        
        # Main frame
        self.chat_frame = ttk.LabelFrame(self.parent_frame, text="🤖 Enhanced Options AI Assistant", padding=10)
        self.chat_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Context display
        context_frame = ttk.Frame(self.chat_frame)
        context_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(context_frame, text="Current Context:", font=('Arial', 9, 'bold')).pack(anchor=tk.W)
        self.context_label = ttk.Label(context_frame, text="No symbol selected", 
                                      font=('Arial', 8), foreground='gray')
        self.context_label.pack(anchor=tk.W)
        
        # Chat display
        self.chat_display = scrolledtext.ScrolledText(
            self.chat_frame, 
            height=20, 
            width=80,
            wrap=tk.WORD,
            font=('Consolas', 9),
            state=tk.DISABLED
        )
        self.chat_display.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # Configure text tags for formatting
        self.chat_display.tag_configure("user", foreground="blue", font=('Arial', 9, 'bold'))
        self.chat_display.tag_configure("assistant", foreground="black", font=('Arial', 9))
        self.chat_display.tag_configure("system", foreground="gray", font=('Arial', 8, 'italic'))
        self.chat_display.tag_configure("header", foreground="darkgreen", font=('Arial', 10, 'bold'))
        self.chat_display.tag_configure("warning", foreground="red", font=('Arial', 9, 'bold'))
        
        # Input frame
        input_frame = ttk.Frame(self.chat_frame)
        input_frame.pack(fill=tk.X)
        
        # Quick action buttons
        button_frame = ttk.Frame(input_frame)
        button_frame.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Button(button_frame, text="Analyze CSP Catalyst", 
                  command=self.analyze_csp_catalyst).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="Portfolio Summary", 
                  command=self.show_portfolio_summary).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="Risk Analysis", 
                  command=self.show_risk_analysis).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="Clear Chat", 
                  command=self.clear_chat).pack(side=tk.RIGHT)
        
        # Input entry and send button
        entry_frame = ttk.Frame(input_frame)
        entry_frame.pack(fill=tk.X)
        
        self.input_entry = ttk.Entry(entry_frame, font=('Arial', 10))
        self.input_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))
        self.input_entry.bind('<Return>', self.send_message)
        
        self.send_button = ttk.Button(entry_frame, text="Send", command=self.send_message)
        self.send_button.pack(side=tk.RIGHT)
        
        # Initial welcome message
        self.add_message("system", "🤖 Enhanced Options AI Assistant Ready")
        self.add_message("system", "Ask me about Cash Secured Put catalysts, market analysis, or portfolio risk!")
        self.add_message("system", "Example: 'What is the news catalyst for this Cash Secured Put?'")
    
    def update_context(self, symbol: str, dashboard_data: Dict[str, Any]):
        """Update the current context for AI analysis"""
        self.current_symbol = symbol
        self.current_dashboard_data = dashboard_data
        
        # Update context display
        context_text = f"Symbol: {symbol}"
        if 'current_price' in dashboard_data:
            context_text += f" | Price: ${dashboard_data['current_price']:.2f}"
        if 'iv_rank' in dashboard_data:
            context_text += f" | IV Rank: {dashboard_data['iv_rank']:.0%}"
        
        self.context_label.config(text=context_text)
        
        # Add context update message
        self.add_message("system", f"📊 Context updated: {symbol}")
    
    def add_message(self, sender: str, message: str):
        """Add a message to the chat display"""
        self.chat_display.config(state=tk.NORMAL)
        
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        if sender == "user":
            self.chat_display.insert(tk.END, f"[{timestamp}] You: ", "user")
            self.chat_display.insert(tk.END, f"{message}\n\n", "assistant")
        elif sender == "assistant":
            self.chat_display.insert(tk.END, f"[{timestamp}] AI Assistant:\n", "user")
            
            # Format the response with appropriate tags
            lines = message.split('\n')
            for line in lines:
                if line.startswith('🎯') or line.startswith('📊') or line.startswith('🚨'):
                    self.chat_display.insert(tk.END, f"{line}\n", "header")
                elif line.startswith('⚠️') or line.startswith('❌'):
                    self.chat_display.insert(tk.END, f"{line}\n", "warning")
                elif line.startswith('=') or line.startswith('-'):
                    self.chat_display.insert(tk.END, f"{line}\n", "system")
                else:
                    self.chat_display.insert(tk.END, f"{line}\n", "assistant")
            
            self.chat_display.insert(tk.END, "\n", "assistant")
        else:  # system
            self.chat_display.insert(tk.END, f"[{timestamp}] ", "system")
            self.chat_display.insert(tk.END, f"{message}\n", "system")
        
        self.chat_display.config(state=tk.DISABLED)
        self.chat_display.see(tk.END)
        
        # Store in chat history
        self.chat_history.append({
            'timestamp': timestamp,
            'sender': sender,
            'message': message
        })
    
    def send_message(self, event=None):
        """Send user message and get AI response"""
        message = self.input_entry.get().strip()
        if not message:
            return
        
        # Clear input
        self.input_entry.delete(0, tk.END)
        
        # Add user message
        self.add_message("user", message)
        
        # Disable send button while processing
        self.send_button.config(state=tk.DISABLED)
        self.input_entry.config(state=tk.DISABLED)
        
        # Process message in background thread
        threading.Thread(target=self.process_message, args=(message,), daemon=True).start()
    
    def process_message(self, message: str):
        """Process user message and generate AI response"""
        try:
            # Determine the type of analysis needed
            response = self.generate_contextual_response(message)
            
            # Queue the response
            self.response_queue.put(('response', response))
            
        except Exception as e:
            error_msg = f"❌ Error processing your request: {str(e)}"
            self.response_queue.put(('error', error_msg))
    
    def generate_contextual_response(self, message: str) -> str:
        """Generate contextual AI response based on the message and current context"""
        message_lower = message.lower()
        
        # If no symbol is selected, provide general guidance
        if not self.current_symbol:
            return ("Please select a symbol from the dashboard first. "
                   "I can provide more specific analysis when I know which stock you're interested in.")
        
        # Route to appropriate analysis based on message content
        if any(keyword in message_lower for keyword in ['catalyst', 'news', 'why', 'moving']):
            return self.ai_assistant.analyze_cash_secured_put_catalyst(self.current_symbol)
        
        elif any(keyword in message_lower for keyword in ['portfolio', 'positions', 'summary']):
            return self.ai_assistant.get_portfolio_summary()
        
        elif any(keyword in message_lower for keyword in ['risk', 'danger', 'concern']):
            risk_analysis = self.ai_assistant.analyze_portfolio_risk()
            return self.format_risk_analysis(risk_analysis)
        
        elif any(keyword in message_lower for keyword in ['roll', 'close', 'manage', 'what should']):
            # Get comprehensive analysis and focus on recommendations
            full_analysis = self.ai_assistant.analyze_cash_secured_put_catalyst(self.current_symbol)
            return self.extract_recommendations(full_analysis)
        
        else:
            # Default to comprehensive analysis
            return self.ai_assistant.analyze_cash_secured_put_catalyst(self.current_symbol)
    
    def format_risk_analysis(self, risk_analysis: Dict[str, Any]) -> str:
        """Format risk analysis for display"""
        lines = []
        lines.append("⚠️  PORTFOLIO RISK ANALYSIS")
        lines.append("=" * 40)
        lines.append(f"Total Positions: {risk_analysis['total_positions']}")
        lines.append(f"Symbols: {risk_analysis['symbols_count']}")
        lines.append(f"Near Expiration (≤7 days): {risk_analysis['near_expiration_positions']}")
        
        if risk_analysis['position_types']:
            lines.append("\nPosition Types:")
            for pos_type, count in risk_analysis['position_types'].items():
                lines.append(f"  • {pos_type.replace('_', ' ').title()}: {count}")
        
        if risk_analysis['expiration_distribution']:
            lines.append("\nExpiration Distribution:")
            for exp_range, count in risk_analysis['expiration_distribution'].items():
                lines.append(f"  • {exp_range}: {count}")
        
        return "\n".join(lines)
    
    def extract_recommendations(self, full_analysis: str) -> str:
        """Extract recommendations section from full analysis"""
        lines = full_analysis.split('\n')
        rec_lines = []
        in_rec_section = False
        
        for line in lines:
            if "STRATEGIC RECOMMENDATIONS" in line:
                in_rec_section = True
                rec_lines.append(line)
            elif in_rec_section and line.startswith("⚠️"):
                break
            elif in_rec_section:
                rec_lines.append(line)
        
        if rec_lines:
            return "\n".join(rec_lines)
        else:
            return "No specific recommendations available. Please try asking about catalyst analysis."
    
    def analyze_csp_catalyst(self):
        """Quick action: Analyze Cash Secured Put catalyst"""
        if not self.current_symbol:
            messagebox.showwarning("No Symbol", "Please select a symbol from the dashboard first.")
            return
        
        self.input_entry.insert(0, f"What is the news catalyst for {self.current_symbol} Cash Secured Put?")
        self.send_message()
    
    def show_portfolio_summary(self):
        """Quick action: Show portfolio summary"""
        self.input_entry.insert(0, "Show me my portfolio summary")
        self.send_message()
    
    def show_risk_analysis(self):
        """Quick action: Show risk analysis"""
        self.input_entry.insert(0, "Analyze my portfolio risk")
        self.send_message()
    
    def clear_chat(self):
        """Clear the chat display"""
        self.chat_display.config(state=tk.NORMAL)
        self.chat_display.delete(1.0, tk.END)
        self.chat_display.config(state=tk.DISABLED)
        self.chat_history.clear()
        
        # Add welcome message back
        self.add_message("system", "🤖 Chat cleared. How can I help you with options analysis?")
    
    def start_response_processor(self):
        """Start the response processor for handling async responses"""
        def process_responses():
            try:
                response_type, response = self.response_queue.get_nowait()
                
                if response_type == 'response':
                    self.add_message("assistant", response)
                elif response_type == 'error':
                    self.add_message("assistant", response)
                
                # Re-enable input
                self.send_button.config(state=tk.NORMAL)
                self.input_entry.config(state=tk.NORMAL)
                self.input_entry.focus()
                
            except queue.Empty:
                pass
            
            # Schedule next check
            self.parent_frame.after(100, process_responses)
        
        # Start the processor
        self.parent_frame.after(100, process_responses)
