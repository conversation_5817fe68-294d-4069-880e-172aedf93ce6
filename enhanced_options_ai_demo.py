"""
Enhanced Options AI Assistant Demo
Demonstrates comprehensive options trading analysis with real market data

This demo shows how to use the Enhanced Options AI Assistant to:
1. Analyze Cash Secured Put positions with news catalysts
2. Get expert-level options trading insights
3. Track portfolio positions with real-time updates
4. Receive strategic recommendations based on market conditions

Usage:
    python enhanced_options_ai_demo.py
"""

import os
from datetime import datetime, timedelta
from enhanced_options_ai_assistant import (
    EnhancedOptionsAIAssistant, 
    OptionsPosition, 
    PositionType
)

def load_api_key():
    """Load FMP API key from file or environment"""
    try:
        with open('fmp.key', 'r') as f:
            return f.read().strip()
    except FileNotFoundError:
        return os.getenv('FMP_API_KEY', 'K63wnAbUDHNPICjzeq3JcEG1vi8Q2oz7')

def create_sample_positions():
    """Create sample Cash Secured Put positions for demo"""
    positions = []
    
    # AAPL Cash Secured Put
    positions.append(OptionsPosition(
        symbol="AAPL",
        position_type=PositionType.CASH_SECURED_PUT,
        entry_date=datetime.now() - timedelta(days=10),
        expiration_date=datetime.now() + timedelta(days=25),
        strike_price=220.0,
        entry_price=3.50,
        quantity=2,
        original_strategy_rationale="Willing to own AAPL at $220, high IV environment"
    ))
    
    # NVDA Cash Secured Put
    positions.append(OptionsPosition(
        symbol="NVDA",
        position_type=PositionType.CASH_SECURED_PUT,
        entry_date=datetime.now() - timedelta(days=5),
        expiration_date=datetime.now() + timedelta(days=15),
        strike_price=130.0,
        entry_price=4.25,
        quantity=1,
        original_strategy_rationale="High IV rank, willing to own NVDA on pullback"
    ))
    
    # AMD Cash Secured Put
    positions.append(OptionsPosition(
        symbol="AMD",
        position_type=PositionType.CASH_SECURED_PUT,
        entry_date=datetime.now() - timedelta(days=3),
        expiration_date=datetime.now() + timedelta(days=18),
        strike_price=140.0,
        entry_price=2.80,
        quantity=3,
        original_strategy_rationale="Earnings play, expecting IV crush post-earnings"
    ))
    
    return positions

def demo_comprehensive_analysis():
    """Demonstrate comprehensive options analysis"""
    print("🚀 Enhanced Options AI Assistant Demo")
    print("=" * 60)
    
    # Initialize the assistant
    api_key = load_api_key()
    assistant = EnhancedOptionsAIAssistant(fmp_api_key=api_key)
    
    # Add sample positions to portfolio
    sample_positions = create_sample_positions()
    for position in sample_positions:
        assistant.add_portfolio_position(position)
    
    print(f"\n📊 Portfolio loaded with {len(sample_positions)} positions")
    
    # Demo 1: Analyze specific Cash Secured Put with news catalysts
    print("\n" + "="*60)
    print("DEMO 1: Cash Secured Put Catalyst Analysis")
    print("="*60)
    
    symbols_to_analyze = ["AAPL", "NVDA", "AMD"]
    
    for symbol in symbols_to_analyze:
        print(f"\n🎯 Analyzing {symbol} Cash Secured Put...")
        print("-" * 40)
        
        try:
            # Update position prices with real-time data
            assistant.update_position_prices(symbol)
            
            # Get comprehensive analysis
            analysis = assistant.analyze_cash_secured_put_catalyst(symbol)
            print(analysis)
            
        except Exception as e:
            print(f"❌ Error analyzing {symbol}: {e}")
        
        print("\n" + "-"*60)
    
    # Demo 2: Portfolio Summary and Risk Analysis
    print("\n" + "="*60)
    print("DEMO 2: Portfolio Summary and Risk Analysis")
    print("="*60)
    
    try:
        # Get portfolio summary
        portfolio_summary = assistant.get_portfolio_summary()
        print(portfolio_summary)
        
        # Get risk analysis
        risk_analysis = assistant.analyze_portfolio_risk()
        print(f"\n📊 RISK ANALYSIS:")
        print(f"Total Positions: {risk_analysis['total_positions']}")
        print(f"Symbols: {risk_analysis['symbols_count']}")
        print(f"Near Expiration (≤7 days): {risk_analysis['near_expiration_positions']}")
        
        print(f"\nPosition Types:")
        for pos_type, count in risk_analysis['position_types'].items():
            print(f"  {pos_type.replace('_', ' ').title()}: {count}")
        
        print(f"\nExpiration Distribution:")
        for exp_range, count in risk_analysis['expiration_distribution'].items():
            print(f"  {exp_range}: {count}")
            
    except Exception as e:
        print(f"❌ Error in portfolio analysis: {e}")

def demo_specific_questions():
    """Demonstrate answering specific trading questions"""
    print("\n" + "="*60)
    print("DEMO 3: Specific Trading Questions")
    print("="*60)
    
    api_key = load_api_key()
    assistant = EnhancedOptionsAIAssistant(fmp_api_key=api_key)
    
    # Sample questions that the assistant can answer
    questions = [
        ("AAPL", "What is the news catalyst for this Cash Secured Put?"),
        ("NVDA", "Should I roll my CSP position given current market conditions?"),
        ("AMD", "What are the key risk factors for my options position?"),
    ]
    
    for symbol, question in questions:
        print(f"\n❓ Question: {question} ({symbol})")
        print("-" * 50)
        
        try:
            # For demo purposes, we'll use the comprehensive analysis
            # In production, you could parse the question and provide more targeted responses
            analysis = assistant.analyze_cash_secured_put_catalyst(symbol)
            
            # Extract relevant sections based on the question
            if "catalyst" in question.lower():
                # Focus on catalyst analysis
                lines = analysis.split('\n')
                catalyst_section = []
                in_catalyst_section = False
                
                for line in lines:
                    if "NEWS CATALYST ANALYSIS" in line:
                        in_catalyst_section = True
                    elif "POSITION ANALYSIS" in line:
                        in_catalyst_section = False
                    
                    if in_catalyst_section:
                        catalyst_section.append(line)
                
                print('\n'.join(catalyst_section[:15]))  # Show first 15 lines
                
            elif "roll" in question.lower():
                # Focus on recommendations
                lines = analysis.split('\n')
                rec_section = []
                in_rec_section = False
                
                for line in lines:
                    if "STRATEGIC RECOMMENDATIONS" in line:
                        in_rec_section = True
                    elif "KEY RISK FACTORS" in line:
                        in_rec_section = False
                    
                    if in_rec_section:
                        rec_section.append(line)
                
                print('\n'.join(rec_section))
                
            elif "risk" in question.lower():
                # Focus on risk factors
                lines = analysis.split('\n')
                risk_section = []
                in_risk_section = False
                
                for line in lines:
                    if "KEY RISK FACTORS" in line:
                        in_risk_section = True
                    elif "KEY TECHNICAL LEVELS" in line:
                        in_risk_section = False
                    
                    if in_risk_section:
                        risk_section.append(line)
                
                print('\n'.join(risk_section))
            
        except Exception as e:
            print(f"❌ Error answering question: {e}")

def main():
    """Run the complete demo"""
    try:
        # Run comprehensive analysis demo
        demo_comprehensive_analysis()
        
        # Run specific questions demo
        demo_specific_questions()
        
        print("\n" + "="*60)
        print("✅ Demo completed successfully!")
        print("="*60)
        
        print("\nKey Features Demonstrated:")
        print("• Real-time market data integration")
        print("• News catalyst identification and analysis")
        print("• Options Greeks and IV analysis")
        print("• Position-specific recommendations")
        print("• Portfolio risk assessment")
        print("• Expert-level options trading insights")
        
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
        print("Please check your API key and internet connection.")

if __name__ == "__main__":
    main()
